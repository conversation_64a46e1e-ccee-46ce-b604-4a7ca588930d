import 'package:flutter_test/flutter_test.dart';
import 'package:fasttime/utils/memory_manager.dart';
import 'package:fasttime/utils/performance_monitor.dart';

void main() {
  group('Memory Management Tests', () {
    test('LimitedCache should respect size limits', () {
      final cache = LimitedCache<String, String>(maxSize: 3);
      
      // Add items up to limit
      cache.put('key1', 'value1');
      cache.put('key2', 'value2');
      cache.put('key3', 'value3');
      
      expect(cache.size, equals(3));
      
      // Add one more item - should evict oldest
      cache.put('key4', 'value4');
      
      expect(cache.size, equals(3));
      expect(cache.get('key1'), isNull); // Should be evicted
      expect(cache.get('key4'), equals('value4')); // Should be present
    });

    test('LimitedCache should update access times', () {
      final cache = LimitedCache<String, String>(maxSize: 2);
      
      cache.put('key1', 'value1');
      cache.put('key2', 'value2');
      
      // Access key1 to update its access time
      cache.get('key1');
      
      // Add key3 - should evict key2 (least recently used)
      cache.put('key3', 'value3');
      
      expect(cache.get('key1'), equals('value1')); // Should still be present
      expect(cache.get('key2'), isNull); // Should be evicted
      expect(cache.get('key3'), equals('value3')); // Should be present
    });

    test('MemoryManager should cache data with expiry', () {
      final manager = MemoryManager();
      
      // Cache some data
      manager.cacheData('test_key', 'test_value');
      
      // Should retrieve cached data
      expect(manager.getCachedData<String>('test_key'), equals('test_value'));
      
      // Clear cache
      manager.clearCache();
      
      // Should return null after clearing
      expect(manager.getCachedData<String>('test_key'), isNull);
    });

    test('MemoryManager should provide memory stats', () {
      final manager = MemoryManager();
      
      final stats = manager.getMemoryStats();
      
      expect(stats, isA<Map<String, int>>());
      expect(stats.containsKey('subscriptions'), isTrue);
      expect(stats.containsKey('timers'), isTrue);
      expect(stats.containsKey('cacheEntries'), isTrue);
    });
  });

  group('Performance Monitor Tests', () {
    test('PerformanceMonitor should be singleton', () {
      final monitor1 = PerformanceMonitor();
      final monitor2 = PerformanceMonitor();
      
      expect(identical(monitor1, monitor2), isTrue);
    });

    test('PerformanceSummary should calculate correctly', () {
      final summary = PerformanceSummary(
        averageMemoryUsage: 100.0,
        maxMemoryUsage: 150.0,
        averageFrameRate: 55.0,
        minFrameRate: 45.0,
        sampleCount: 10,
      );
      
      expect(summary.averageMemoryUsage, equals(100.0));
      expect(summary.maxMemoryUsage, equals(150.0));
      expect(summary.averageFrameRate, equals(55.0));
      expect(summary.minFrameRate, equals(45.0));
      expect(summary.sampleCount, equals(10));
    });

    test('PerformanceMetric should store data correctly', () {
      final timestamp = DateTime.now();
      final metric = PerformanceMetric(
        timestamp: timestamp,
        memoryUsage: 75.5,
        frameRate: 58.2,
      );
      
      expect(metric.timestamp, equals(timestamp));
      expect(metric.memoryUsage, equals(75.5));
      expect(metric.frameRate, equals(58.2));
    });
  });

  group('Integration Tests', () {
    test('Memory management should work together', () {
      final manager = MemoryManager();
      final cache = LimitedCache<String, int>(maxSize: 5);
      
      // Test that both can work together without conflicts
      manager.cacheData('global_key', 'global_value');
      cache.put('local_key', 42);
      
      expect(manager.getCachedData<String>('global_key'), equals('global_value'));
      expect(cache.get('local_key'), equals(42));
      
      // Clean up
      manager.clearCache();
      cache.clear();
      
      expect(manager.getCachedData<String>('global_key'), isNull);
      expect(cache.get('local_key'), isNull);
    });
  });
}
