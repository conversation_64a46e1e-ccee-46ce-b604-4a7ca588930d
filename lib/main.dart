import 'package:fasttime/blocs/settings/settings_bloc.dart';
import 'package:fasttime/services/achievement_service.dart';
import 'package:fasttime/services/body_metrics_service.dart';
import 'package:fasttime/services/fasting_service.dart';
import 'package:fasttime/utils/performance_monitor.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'blocs/achievement/achievement_bloc.dart';
import 'blocs/auth/auth_bloc.dart';
import 'blocs/auth/auth_event.dart';
import 'blocs/body_metrics/body_metrics_bloc.dart';
import 'blocs/fasting/fasting_bloc.dart';
import 'blocs/timer/timer_bloc.dart';
import 'models/achievement.dart' show AchievementAdapter;
import 'models/body_metric.dart' show BodyMetricAdapter;
import 'models/fasting_day.dart' show FastingDayAdapter;
import 'route/router_config.dart';
import 'package:flutter/material.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Start performance monitoring early
  PerformanceMonitor().startMonitoring();

  // Initialize Firebase first
  await Firebase.initializeApp();

  // Initialize Hive with adapters
  await _initializeHive();

  runApp(const MyApp());
}

Future<void> _initializeHive() async {
  await Hive.initFlutter();

  // Register adapters only once
  if (!Hive.isAdapterRegistered(0)) {
    Hive.registerAdapter(FastingDayAdapter());
  }

  if (!Hive.isAdapterRegistered(1)) {
    Hive.registerAdapter(BodyMetricAdapter());
  }

  if (!Hive.isAdapterRegistered(2)) {
    Hive.registerAdapter(AchievementAdapter());
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Map<String, dynamic>>(
      future: _initializeServices(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return MaterialApp(
            home: Scaffold(
              backgroundColor: const Color(0xFF121212),
              body: const Center(
                child: CircularProgressIndicator(color: Colors.white),
              ),
            ),
            debugShowCheckedModeBanner: false,
          );
        }

        if (snapshot.hasError) {
          return MaterialApp(
            home: Scaffold(
              backgroundColor: const Color(0xFF121212),
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.error, color: Colors.red, size: 48),
                    const SizedBox(height: 16),
                    Text(
                      'Failed to initialize app',
                      style: GoogleFonts.dmSans(
                        color: Colors.white,
                        fontSize: 18,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      snapshot.error.toString(),
                      style: GoogleFonts.dmSans(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
            debugShowCheckedModeBanner: false,
          );
        }

        final services = snapshot.data!;
        return MultiBlocProvider(
          providers: [
            BlocProvider<AuthBloc>(
              create: (context) => AuthBloc()..add(AuthCheckRequested()),
            ),
            BlocProvider<SettingsBloc>(create: (context) => SettingsBloc()),
            BlocProvider<FastingBloc>(
              create: (context) => FastingBloc(
                services['fastingService'] as FastingService,
                context.read<SettingsBloc>(),
              ),
            ),
            BlocProvider<BodyMetricsBloc>(
              create: (context) => BodyMetricsBloc(
                services['bodyMetricsService'] as BodyMetricsService,
              ),
            ),
            BlocProvider<AchievementBloc>(
              create: (context) => AchievementBloc(
                services['achievementService'] as AchievementService,
                services['fastingService'] as FastingService,
              ),
            ),
            BlocProvider<TimerBloc>(create: (context) => TimerBloc()),
          ],
          child: MaterialApp.router(
            title: 'FasTime',
            routerConfig: router,
            debugShowCheckedModeBanner: false,
          ),
        );
      },
    );
  }

  Future<Map<String, dynamic>> _initializeServices() async {
    // Initialize services sequentially to reduce memory pressure
    final fastingService = FastingService();
    await fastingService.init();

    final bodyMetricsService = BodyMetricsService();
    await bodyMetricsService.init();

    final achievementService = AchievementService();
    await achievementService.init();

    return {
      'fastingService': fastingService,
      'bodyMetricsService': bodyMetricsService,
      'achievementService': achievementService,
    };
  }
}
