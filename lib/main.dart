import 'package:fasttime/blocs/settings/settings_bloc.dart';
import 'package:fasttime/services/achievement_service.dart';
import 'package:fasttime/services/body_metrics_service.dart';
import 'package:fasttime/services/encryption_service.dart';
import 'package:fasttime/services/fasting_service.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'blocs/achievement/achievement_bloc.dart';
import 'blocs/auth/auth_bloc.dart';
import 'blocs/auth/auth_event.dart';
import 'blocs/body_metrics/body_metrics_bloc.dart';
import 'blocs/fasting/fasting_bloc.dart';
import 'blocs/fasting/fasting_event.dart';
import 'blocs/settings/settings_event.dart';
import 'blocs/timer/timer_bloc.dart';
import 'models/achievement.dart' show AchievementAdapter;
import 'models/body_metric.dart' show BodyMetricAdapter;
import 'models/fasting_day.dart' show FastingDayAdapter;
import 'route/router_config.dart';
import 'package:flutter/material.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();

  // Initialize Hive
  await Hive.initFlutter();

  if (!Hive.isAdapterRegistered(0)) {
    Hive.registerAdapter(FastingDayAdapter());
  }

  if (!Hive.isAdapterRegistered(1)) {
    Hive.registerAdapter(BodyMetricAdapter());
  }

  if (!Hive.isAdapterRegistered(2)) {
    Hive.registerAdapter(AchievementAdapter());
  }

  // Initialize services with encryption
  final fastingService = FastingService();
  final bodyMetricsService = BodyMetricsService();
  final achievementService = AchievementService();

  await Future.wait([
    fastingService.init(),
    bodyMetricsService.init(),
    achievementService.init(),
  ]);


  runApp(
    MultiBlocProvider(
      providers: [
        BlocProvider<AuthBloc>(
          create: (context) => AuthBloc()..add(AuthCheckRequested()),
        ),
        BlocProvider<SettingsBloc>(
          create: (context) {
            final settingsBloc = SettingsBloc();
            settingsBloc.add(LoadSettings());
            return settingsBloc;
          },
        ),
        BlocProvider<FastingBloc>(
          create: (context) {
            final fastingBloc = FastingBloc(
              fastingService,
              context.read<SettingsBloc>(),
            );
            fastingBloc.add(LoadFastingDay(DateTime.now()));
            return fastingBloc;
          },
        ),
        BlocProvider<BodyMetricsBloc>(
          create: (context) => BodyMetricsBloc(bodyMetricsService),
        ),
        BlocProvider<AchievementBloc>(
          create: (context) => AchievementBloc(
            achievementService,
            fastingService
          ),
        ),
        BlocProvider<TimerBloc>(
          create: (context) => TimerBloc(),
        ),

      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'FasTime',
      routerConfig: router,
      debugShowCheckedModeBanner: false,
    );
  }
}
