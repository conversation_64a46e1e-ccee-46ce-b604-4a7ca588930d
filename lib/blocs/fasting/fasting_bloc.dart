import 'package:fasttime/services/error_service.dart';
import 'package:fasttime/utils/time_utils.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../models/fasting_day.dart';
import '../../services/fasting_service.dart';
import 'fasting_event.dart';
import 'fasting_state.dart';
import 'package:fasttime/blocs/settings/settings_bloc.dart';
import 'package:fasttime/utils/subscription_manager.dart';

class FastingBloc extends Bloc<FastingEvent, FastingState> {
  final FastingService _fastingService;
  final SettingsBloc _settingsBloc;
  final SubscriptionManager _subscriptions = SubscriptionManager();

  // Cache for completed days to avoid repeated filtering
  List<FastingDay>? _completedDaysCache;
  DateTime? _lastCacheUpdate;

  FastingBloc(this._fastingService, this._settingsBloc)
      : super(const FastingState()) {
    on<LoadFastingDay>(_onLoadFastingDay);
    on<UpdateFastingWindow>(_onUpdateFastingWindow);
    on<UpdateDailyRoutine>(_onUpdateDailyRoutine);
    on<LoadAllCompletedFastingDays>(_onLoadAllCompletedFastingDays);
    on<DeleteFastingDay>(_onDeleteFastingDay);

    _setupDataListener();
  }

  void _setupDataListener() {
    _subscriptions.add(_fastingService.watchFastingDays().listen((_) {
      _invalidateCache();
      // Use a single event to reload all necessary data
      add(LoadFastingDay(DateTime.now()));
    }));
  }

  void _invalidateCache() {
    _completedDaysCache = null;
    _lastCacheUpdate = null;
  }

  List<FastingDay> _getCompletedDays() {
    final now = DateTime.now();

    // Return cached data if it's still valid (less than 1 minute old)
    if (_completedDaysCache != null &&
        _lastCacheUpdate != null &&
        now.difference(_lastCacheUpdate!).inMinutes < 1) {
      return _completedDaysCache!;
    }

    // Refresh cache
    _completedDaysCache = _fastingService
        .getAllFastingDays()
        .where((day) => day.completed)
        .toList()
      ..sort((a, b) => b.date.compareTo(a.date));

    _lastCacheUpdate = now;
    return _completedDaysCache!;
  }

  Future<void> _onLoadFastingDay(
      LoadFastingDay event, Emitter<FastingState> emit) async {
    try {
      emit(state.copyWith(isLoading: true, error: null));

      final fastingDay = _fastingService.getFastingDay(event.date);

      if (fastingDay == null) {
        final newDay = await _createNewFastingDay(event.date);
        emit(state.copyWith(
          currentDay: newDay,
          completedDays: _getCompletedDays(),
          isLoading: false,
        ));
        return;
      }

      // Check for auto-completion
      final updatedDay = await _checkAndAutoComplete(fastingDay);

      emit(state.copyWith(
        currentDay: updatedDay,
        completedDays: _getCompletedDays(),
        isLoading: false,
      ));
    } catch (e) {
      emit(state.copyWith(
          error: ErrorService.formatErrorMessage(e),
          isLoading: false
      ));
    }
  }

  Future<FastingDay> _createNewFastingDay(DateTime date) async {
    final defaultProtocol = _settingsBloc.state.fastingProtocol;

    final newDay = FastingDay(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      date: date,
      startTime: '--:--',
      endTime: '--:--',
      isActive: false,
      completed: false,
      fastingProtocol: defaultProtocol,
    );

    await _fastingService.saveFastingDay(newDay);
    return newDay;
  }

  Future<FastingDay> _checkAndAutoComplete(FastingDay fastingDay) async {
    if (!fastingDay.isActive || fastingDay.completed) {
      return fastingDay;
    }

    try {
      final startDateTime = getStartDateTime(fastingDay);
      final endTimeParts = fastingDay.endTime.split(':');
      final endDateTime = DateTime(
        startDateTime.year,
        startDateTime.month,
        startDateTime.day + 1,
        int.parse(endTimeParts[0]),
        int.parse(endTimeParts[1]),
      );

      if (DateTime.now().isAfter(endDateTime)) {
        final actualDuration = endDateTime.difference(startDateTime).inMinutes;

        await _fastingService.updateFastingWindow(
          date: fastingDay.date,
          startTime: fastingDay.startTime,
          endTime: fastingDay.endTime,
          isActive: false,
          completed: true,
          startDate: startDateTime,
          endDate: endDateTime,
          actualFastingMinutes: actualDuration,
          fastingProtocol: fastingDay.fastingProtocol,
        );

        _invalidateCache(); // Invalidate cache after update
        return _fastingService.getFastingDay(fastingDay.date) ?? fastingDay;
      }
    } catch (e) {
      // Log error but don't throw - return original day
      print('Error in auto-completion check: $e');
    }

    return fastingDay;
  }

  Future<void> _onUpdateFastingWindow(
      UpdateFastingWindow event, Emitter<FastingState> emit) async {
    try {
      emit(state.copyWith(isLoading: true, error: null));

      final actualMinutes = _calculateActualMinutes(event);
      final fastingProtocol = event.fastingProtocol ??
          state.currentDay?.fastingProtocol ??
          '16:8';

      await _fastingService.updateFastingWindow(
        date: event.date,
        startTime: event.startTime,
        endTime: event.endTime,
        isActive: event.isActive ?? false,
        completed: event.completed ?? false,
        startDate: event.startDate,
        endDate: event.endDate,
        actualFastingMinutes: actualMinutes,
        fastingProtocol: fastingProtocol,
      );

      _invalidateCache(); // Invalidate cache after update

      final updatedDay = _fastingService.getFastingDay(event.date);
      emit(state.copyWith(
        currentDay: updatedDay,
        completedDays: _getCompletedDays(),
        isLoading: false,
      ));
    } catch (e) {
      emit(state.copyWith(
          error: ErrorService.formatErrorMessage(e),
          isLoading: false
      ));
    }
  }

  int? _calculateActualMinutes(UpdateFastingWindow event) {
    if (event.completed == true &&
        event.startDate != null &&
        event.endDate != null) {
      return event.endDate!.difference(event.startDate!).inMinutes;
    }
    return null;
  }

  Future<void> _onUpdateDailyRoutine(
      UpdateDailyRoutine event, Emitter<FastingState> emit) async {
    try {
      emit(state.copyWith(isLoading: true, error: null));

      await _fastingService.updateDailyRoutine(
        date: event.date,
        waterIntake: event.waterIntake,
        steps: event.steps,
        exercise: event.exercise,
      );

      final updatedDay = _fastingService.getFastingDay(event.date);
      emit(state.copyWith(currentDay: updatedDay, isLoading: false));
    } catch (e) {
      emit(state.copyWith(
          error: ErrorService.formatErrorMessage(e),
          isLoading: false
      ));
    }
  }

  void _onLoadAllCompletedFastingDays(
      LoadAllCompletedFastingDays event,
      Emitter<FastingState> emit,
      ) {
    try {
      emit(state.copyWith(isLoading: true, error: null));
      emit(state.copyWith(
          completedDays: _getCompletedDays(),
          isLoading: false
      ));
    } catch (e) {
      emit(state.copyWith(
          error: ErrorService.formatErrorMessage(e),
          isLoading: false
      ));
    }
  }

  Future<void> _onDeleteFastingDay(
      DeleteFastingDay event, Emitter<FastingState> emit) async {
    try {
      await _fastingService.deleteFastingDay(event.fastingDay);
      _invalidateCache(); // Invalidate cache after deletion

      emit(state.copyWith(completedDays: _getCompletedDays()));
    } catch (e) {
      emit(state.copyWith(error: ErrorService.formatErrorMessage(e)));
    }
  }

  @override
  Future<void> close() {
    _subscriptions.cancelAll();
    _invalidateCache();
    return super.close();
  }
}