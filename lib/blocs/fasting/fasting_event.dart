// Events
import 'package:equatable/equatable.dart';

import '../../models/fasting_day.dart';

abstract class FastingEvent extends Equatable {
  const FastingEvent();

  @override
  List<Object?> get props => [];
}

class LoadFastingDay extends FastingEvent {
  final DateTime date;
  const LoadFastingDay(this.date);

  @override
  List<Object?> get props => [date];
}

class UpdateDailyRoutine extends FastingEvent {
  final DateTime date;
  final double? waterIntake;
  final int? steps;
  final double? exercise;

  const UpdateDailyRoutine({
    required this.date,
    this.waterIntake,
    this.steps,
    this.exercise,
  });

  @override
  List<Object?> get props => [date, waterIntake, steps, exercise];
}

class UpdateFastingWindow extends FastingEvent {
  final DateTime date;
  final String startTime;
  final String endTime;
  final bool? isActive;
  final bool? completed;
  final DateTime? startDate;
  final DateTime? endDate;
  final int? actualFastingMinutes;
  final String? fastingProtocol;

  const UpdateFastingWindow({
    required this.date,
    required this.startTime,
    required this.endTime,
    this.isActive,
    this.completed,
    this.startDate,
    this.endDate,
    this.actualFastingMinutes,
    this.fastingProtocol,
  });

  @override
  List<Object?> get props => [
    date,
    startTime,
    endTime,
    isActive,
    completed,
    startDate,
    endDate,
    actualFastingMinutes,
    fastingProtocol,
  ];
}

class LoadAllCompletedFastingDays extends FastingEvent {}

class DeleteFastingDay extends FastingEvent {
  final FastingDay fastingDay;
  const DeleteFastingDay(this.fastingDay);

  @override
  List<Object?> get props => [fastingDay];
}
