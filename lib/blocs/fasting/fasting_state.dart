
// State
import 'package:equatable/equatable.dart';

import '../../models/fasting_day.dart';

class FastingState extends Equatable {
  final FastingDay? currentDay;
  final List<FastingDay> completedDays;
  final bool isLoading;
  final String? error;

  const FastingState({
    this.currentDay,
    this.completedDays = const [],
    this.isLoading = false,
    this.error,
  });

  FastingState copyWith({
    FastingDay? currentDay,
    List<FastingDay>? completedDays,
    bool? isLoading,
    String? error,
  }) {
    return FastingState(
      currentDay: currentDay ?? this.currentDay,
      completedDays: completedDays ?? this.completedDays,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  @override
  List<Object?> get props => [currentDay, completedDays, isLoading, error];
}
