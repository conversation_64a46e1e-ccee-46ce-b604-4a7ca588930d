  import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../models/fasting_day.dart';

DateTime getStartDateTime(FastingDay? fastingDay) {
    if (fastingDay == null || fastingDay.startTime == '--:--') {
      return DateTime.now();
    }

    final timeComponents = fastingDay.startTime.split(':');
    final hours = int.parse(timeComponents[0]);
    final minutes = int.parse(timeComponents[1]);

    final now = DateTime.now();
    var startDate = DateTime(
      fastingDay.date.year,
      fastingDay.date.month,
      fastingDay.date.day,
      hours,
      minutes,
    );

    // If there's a specific start date recorded, use that instead
    if (fastingDay.startDate != null) {
      return fastingDay.startDate!;
    }

    // If the start time is in the future of the current day,
    // use the previous day's date
    if (!fastingDay.isActive && startDate.isAfter(now)) {
      startDate = startDate.subtract(const Duration(days: 1));
    }

    return startDate;
  }

  // Get fasting window end time as DateTime
  DateTime getEndDateTime(FastingDay? fastingDay) {
    if (fastingDay == null || fastingDay.endTime == '--:--') {
      return DateTime.now();
    }

    final timeComponents = fastingDay.endTime.split(':');
    final hours = int.parse(timeComponents[0]);
    final minutes = int.parse(timeComponents[1]);

    final startDateTime = getStartDateTime(fastingDay);
    var endDate = DateTime(
      fastingDay.date.year,
      fastingDay.date.month,
      fastingDay.date.day,
      hours,
      minutes,
    );

    // If end time is before start time, it means it ends the next day
    if (endDate.isBefore(startDateTime)) {
      endDate = endDate.add(const Duration(days: 1));
    }

    return endDate;
  }

    String formatDuration(Duration duration) {
      final hours = duration.inHours;
      final minutes = (duration.inMinutes % 60).toString().padLeft(2, '0');
      final seconds = (duration.inSeconds % 60).toString().padLeft(2, '0');
      return '$hours:$minutes:$seconds';
    }

  String formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 60) {
      return "${difference.inMinutes}m ago";
    } else if (difference.inHours < 24) {
      return "${difference.inHours}h ${difference.inMinutes % 60}m ago";
    } else {
      return "${difference.inDays}d ago";
    }
  }

  String getRemainingTime(FastingDay? fastingDay) {
    try {
      if (fastingDay == null) return "--:--:--";

      final now = DateTime.now();
      final startDateTime = getStartDateTime(fastingDay);
      final endDateTime = getEndDateTime(fastingDay);
      final targetDuration =
      Duration(hours: int.parse(fastingDay.fastingProtocol.split(':')[0]));

      // If not active
      if (!fastingDay.isActive) {
        // If start time is in future
        if (now.isBefore(startDateTime)) {
          final difference = startDateTime.difference(now);
          return "Starts in ${formatDuration(difference)}";
        }
        // If start time is in past but not active
        else {
          return "Ready to start";
        }
      }

      // If completed or past end time
      if (fastingDay.completed || now.isAfter(endDateTime)) {
        final actualDuration = fastingDay.endDate != null
            ? fastingDay.endDate!.difference(startDateTime)
            : endDateTime.difference(startDateTime);

        final overtime = actualDuration - targetDuration;
        if (overtime.inMinutes > 0) {
          return "✅ Completed! (+${formatDuration(overtime)})";
        }
        return "✅ Completed!";
      }

      // Only show remaining time if fast is active and not in future
      if (fastingDay.isActive && !now.isBefore(startDateTime)) {
        final difference = endDateTime.difference(now);
        final elapsed = now.difference(startDateTime);

        // If already past target duration but still fasting
        if (elapsed > targetDuration) {
          final overtime = elapsed - targetDuration;
          return "${formatDuration(difference)} (+${formatDuration(overtime)})";
        }

        return formatDuration(difference);
      }

      return "--:--:--";
    } catch (e) {
      return "--:--:--";
    }
  }

  double calculateProgress(FastingDay? fastingDay) {
    if (fastingDay == null) return 0.0;

    try {
      final now = DateTime.now();
      final startDateTime = getStartDateTime(fastingDay);
      final endDateTime = getEndDateTime(fastingDay);

      if (!fastingDay.isActive && now.isBefore(startDateTime)) {
        return 0.0;
      }

      if (fastingDay.completed || now.isAfter(endDateTime)) {
        return 1.0;
      }

      if (fastingDay.isActive) {
        final totalDuration = endDateTime.difference(startDateTime).inSeconds;
        final elapsedDuration = now.difference(startDateTime).inSeconds;

        if (totalDuration <= 0) return 0.0;

        final progress = (elapsedDuration / totalDuration).clamp(0.0, 1.0);
        return progress;
      }

      return 0.0;
    } catch (e) {
      return 0.0;
    }
  }

  String timeToString(TimeOfDay timeOfDay) {
    return '${timeOfDay.hour.toString().padLeft(2, '0')}:${timeOfDay.minute.toString().padLeft(2, '0')}';
  }

