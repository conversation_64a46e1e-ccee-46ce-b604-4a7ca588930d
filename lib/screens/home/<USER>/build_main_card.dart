import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../models/fasting_day.dart';
import '../../../utils/text_utils.dart';
import '../../../utils/time_utils.dart';
import 'build_timeline.dart';

Widget buildMainCard(
    bool isActive,
    int currentStageIndex,
    List<IconData> stageIcons,
    FastingDay? fastingDay,
    ) {
  return ClipRRect(
    borderRadius: BorderRadius.circular(20),
    child: BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.05),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 15,
              offset: Offset(0, 5),
            ),
          ],
          border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Progress timeline
            buildTimeline(isActive, currentStageIndex, stageIcons),

            // Current stage info if active
            if (isActive)
              _buildCurrentStageCard(
                currentStageIndex,
                stageIcons,
                fastingDay,
                DateTime.now()
              ),

            // All stages list
            _buildAllStagesList(isActive, currentStageIndex, stageIcons),
          ],
        ),
      ),
    ),
  );
}

Widget _buildAllStagesList(
    bool isActive,
    int currentStageIndex,
    List<IconData> stageIcons,
    ) {
  return Theme(
    data: ThemeData(
      dividerColor: Colors.transparent,
      unselectedWidgetColor: Colors.white70,
    ),
    child: ExpansionTile(
      tilePadding: EdgeInsets.symmetric(horizontal: 16),
      childrenPadding: EdgeInsets.only(left: 8, right: 8, bottom: 16),
      title: Row(
        children: [
          Icon(Icons.list_alt, color: Colors.white70, size: 18),
          SizedBox(width: 8),
          Text(
            'View All Stages',
            style: GoogleFonts.dmSans(color: Colors.white70, fontSize: 14),
          ),
        ],
      ),
      trailing: Container(
        padding: EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: Color(0xFF444444),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Icon(
          Icons.keyboard_arrow_down,
          color: Colors.white70,
          size: 18,
        ),
      ),
      children: fastingStages.asMap().entries.map((entry) {
        final i = entry.key;
        final stage = entry.value;
        final bool isCurrentStage = isActive && i == currentStageIndex;
        final bool isPastStage = isActive && i < currentStageIndex;
        final bool isFutureStage = isActive && i > currentStageIndex;

        return Container(
          margin: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
          decoration: BoxDecoration(
            color: isCurrentStage
                ? (stage['color'] as Color).withOpacity(0.15)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(12),
            border: isCurrentStage
                ? Border.all(
              color: (stage['color'] as Color).withOpacity(0.3),
              width: 1,
            )
                : null,
          ),
          child: ListTile(
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            leading: Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isCurrentStage
                    ? (stage['color'] as Color).withOpacity(0.2)
                    : isPastStage
                    ? (stage['color'] as Color).withOpacity(0.8)
                    : Color(0xFF444444),
                boxShadow: isCurrentStage
                    ? [
                  BoxShadow(
                    color: (stage['color'] as Color).withOpacity(0.3),
                    blurRadius: 8,
                    spreadRadius: 1,
                  ),
                ]
                    : null,
              ),
              child: Center(
                child: AnimatedSwitcher(
                  duration: Duration(milliseconds: 300),
                  transitionBuilder:
                      (Widget child, Animation<double> animation) {
                    return ScaleTransition(
                      scale: animation,
                      child: child,
                    );
                  },
                  child: isCurrentStage
                      ? Icon(
                    stageIcons[i],
                    key: ValueKey('current-${stageIcons[i]}'),
                    color: stage['color'] as Color,
                    size: 20,
                  )
                      : isPastStage
                      ? Icon(
                    Icons.check_circle,
                    key: ValueKey('past'),
                    color: Colors.white,
                    size: 18,
                  )
                      : Icon(
                    stageIcons[i],
                    key: ValueKey('future-${stageIcons[i]}'),
                    color: Colors.white.withOpacity(0.5),
                    size: 18,
                  ),
                ),
              ),
            ),
            title: Text(
              stage['name'] as String,
              style: GoogleFonts.dmSans(
                color: Colors.white,
                fontWeight: isCurrentStage
                    ? FontWeight.bold
                    : FontWeight.normal,
              ),
            ),
            subtitle: Text(
              stage['description'] as String,
              style: GoogleFonts.dmSans(
                color: isCurrentStage ? Colors.white70 : Colors.white60,
                fontSize: 12,
              ),
            ),
            trailing: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: (stage['color'] as Color).withOpacity(0.15),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    '${stage['hours']}h',
                    style: GoogleFonts.dmSans(
                      color: stage['color'] as Color,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (isCurrentStage)
                  Container(
                    margin: EdgeInsets.only(top: 4),
                    padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.green.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      'ACTIVE',
                      style: GoogleFonts.dmSans(
                        color: Colors.green,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      }).toList(),
    ),
  );
}


Widget _buildCurrentStageCard(
    int currentStageIndex,
    List<IconData> stageIcons,
    FastingDay? fastingDay,
    DateTime? now
    ) {
  return TweenAnimationBuilder<double>(
    tween: Tween<double>(begin: 0.8, end: 1.0),
    duration: Duration(milliseconds: 300),
    curve: Curves.easeOutQuart,
    builder: (context, value, child) {
      return Transform.scale(
        scale: value,
        child: Opacity(opacity: value, child: child),
      );
    },
    child: Container(
      margin: EdgeInsets.fromLTRB(16, 0, 16, 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF353535), Color(0xFF252525)],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: (fastingStages[currentStageIndex]['color'] as Color)
                .withOpacity(0.15),
            blurRadius: 10,
            spreadRadius: 1,
          ),
        ],
        border: Border.all(
          color: (fastingStages[currentStageIndex]['color'] as Color)
              .withOpacity(0.5),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Animated icon container
                    TweenAnimationBuilder<double>(
                      tween: Tween<double>(begin: 0.0, end: 1.0),
                      duration: Duration(milliseconds: 800),
                      curve: Curves.elasticOut,
                      builder: (context, value, child) {
                        return Transform.scale(scale: value, child: child);
                      },
                      child: Container(
                        padding: EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color:
                          (fastingStages[currentStageIndex]['color']
                          as Color)
                              .withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color:
                              (fastingStages[currentStageIndex]['color']
                              as Color)
                                  .withOpacity(0.3),
                              blurRadius: 8,
                              spreadRadius: -2,
                            ),
                          ],
                        ),
                        child: Icon(
                          stageIcons[currentStageIndex],
                          color:
                          fastingStages[currentStageIndex]['color']
                          as Color,
                          size: 28,
                        ),
                      ),
                    ),
                    SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            fastingStages[currentStageIndex]['name']
                            as String,
                            style: GoogleFonts.inter(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              letterSpacing: 0.5,
                            ),
                          ),
                          SizedBox(height: 4),
                          Wrap(
                            spacing: 8,
                            children: [
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 3,
                                ),
                                decoration: BoxDecoration(
                                  color:
                                  (fastingStages[currentStageIndex]['color']
                                  as Color)
                                      .withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  '${fastingStages[currentStageIndex]['hours']}+ hours',
                                  style: GoogleFonts.dmSans(
                                    color:
                                    fastingStages[currentStageIndex]['color']
                                    as Color,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              if (fastingDay != null)
                                _buildElapsedBadge(fastingDay, now!),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 16),
                Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.05),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    fastingStages[currentStageIndex]['description'] as String,
                    style: GoogleFonts.dmSans(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 14,
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          ),
          if (currentStageIndex < fastingStages.length - 1)
            Container(
              margin: EdgeInsets.fromLTRB(16, 0, 16, 16),
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              decoration: BoxDecoration(
                color: Color(0xFF444444).withOpacity(0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.white.withOpacity(0.05),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(Icons.upcoming, color: Colors.white70, size: 18),
                  SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Coming up: ${fastingStages[currentStageIndex + 1]['name']}',
                          style: GoogleFonts.dmSans(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'In ${fastingStages[currentStageIndex + 1]['hours'] - fastingStages[currentStageIndex]['hours']} hours',
                          style: GoogleFonts.dmSans(
                            color: Colors.white70,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                      color:
                      (fastingStages[currentStageIndex + 1]['color']
                      as Color)
                          .withOpacity(0.2),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      stageIcons[currentStageIndex + 1],
                      color:
                      fastingStages[currentStageIndex + 1]['color']
                      as Color,
                      size: 20,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    ),
  );
}


Widget _buildElapsedBadge(FastingDay fastingDay, DateTime now) {
  final startDateTime = getStartDateTime(fastingDay);
  final elapsed = now.difference(startDateTime);

  if (!fastingDay.isActive) return SizedBox.shrink();

  // Format the duration to show hours and minutes
  final hours = elapsed.inHours;
  final minutes = elapsed.inMinutes % 60;

  return Container(
    margin: EdgeInsets.only(left: 8),
    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
    decoration: BoxDecoration(
      color: Colors.green.withOpacity(0.2),
      borderRadius: BorderRadius.circular(12),
    ),
    child: Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(Icons.timer, color: Colors.green, size: 12),
        SizedBox(width: 4),
        Text(
          '${hours}h ${minutes}m',
          style: GoogleFonts.dmSans(
            color: Colors.green,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    ),
  );
}
