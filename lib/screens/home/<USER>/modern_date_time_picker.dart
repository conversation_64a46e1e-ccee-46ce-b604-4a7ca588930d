import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter/services.dart';

class ModernDateTimePicker extends StatefulWidget {
  final DateTime initialDateTime;
  final String label;
  final Function(DateTime) onDateTimeSelected;
  final Color accentColor;
  final Color backgroundColor;
  final DateTime? firstDate;
  final DateTime? lastDate;

  const ModernDateTimePicker({
    Key? key,
    required this.initialDateTime,
    required this.label,
    required this.onDateTimeSelected,
    this.accentColor = const Color(0xFF6B4EFF),
    this.backgroundColor = const Color(0xFF191919),
    this.firstDate,
    this.lastDate,
  }) : super(key: key);

  static Future<DateTime?> show({
    required BuildContext context,
    required DateTime initialDateTime,
    required String label,
    Color accentColor = const Color(0xFF6B4EFF),
    Color backgroundColor = const Color(0xFF191919),
    DateTime? firstDate,
    DateTime? lastDate,
  }) async {
    return await showModalBottomSheet<DateTime>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => FractionallySizedBox(
        heightFactor: 0.8,
        child: ModernDateTimePicker(
          initialDateTime: initialDateTime,
          label: label,
          onDateTimeSelected: (dateTime) {
            // Use GoRouter to pop instead of Navigator
            GoRouter.of(context).pop<DateTime>(dateTime);
          },
          accentColor: accentColor,
          backgroundColor: backgroundColor,
          firstDate: firstDate,
          lastDate: lastDate,
        ),
      ),
    );
  }

  @override
  State<ModernDateTimePicker> createState() => _ModernDateTimePickerState();
}

class _ModernDateTimePickerState extends State<ModernDateTimePicker>
    with TickerProviderStateMixin {
  late DateTime _selectedDateTime;
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _selectedDateTime = widget.initialDateTime;
    _tabController = TabController(length: 2, vsync: this);

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    _animation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutQuart),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: SafeArea(
        child: FadeTransition(
          opacity: _animation,
          child: Padding(
            padding: const EdgeInsets.fromLTRB(24.0, 16.0, 24.0, 24.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHandle(),
                const SizedBox(height: 8),
                _buildHeader(),
                const SizedBox(height: 16),
                _buildSelectedDateTime(),
                const SizedBox(height: 16),
                _buildTabBar(),
                SizedBox(
                  height: MediaQuery.of(context).size.height * 0.4,
                  child: TabBarView(
                    controller: _tabController,
                    physics: const NeverScrollableScrollPhysics(),
                    children: [
                      _buildDateContent(),
                      _buildTimeContent(),
                    ],
                  ),
                ),
                _buildBottomButtons(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHandle() {
    return Center(
      child: Container(
        width: 40,
        height: 5,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          borderRadius: BorderRadius.circular(2.5),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          widget.label,
          style: GoogleFonts.dmSans(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        IconButton(
          onPressed: () => GoRouter.of(context).pop(),
          icon: const Icon(Icons.close, color: Colors.white),
          style: IconButton.styleFrom(
            backgroundColor: Colors.white.withOpacity(0.1),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSelectedDateTime() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
      decoration: BoxDecoration(
        color: widget.accentColor.withOpacity(0.15),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: widget.accentColor.withOpacity(0.3),
          width: 1.5,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.calendar_today_rounded,
            color: widget.accentColor,
            size: 24,
          ),
          const SizedBox(width: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Selected Date & Time',
                style: GoogleFonts.dmSans(
                  fontSize: 14,
                  color: Colors.white.withOpacity(0.7),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '${_getMonthName(_selectedDateTime.month)} ${_selectedDateTime.day}, ${_selectedDateTime.year} at ${_formatTime(_selectedDateTime)}',
                style: GoogleFonts.dmSans(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _getMonthName(int month) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return months[month - 1];
  }

  String _formatTime(DateTime dateTime) {
    final hour = dateTime.hour > 12
        ? dateTime.hour - 12
        : dateTime.hour == 0
            ? 12
            : dateTime.hour;
    final minute = dateTime.minute.toString().padLeft(2, '0');
    final period = dateTime.hour >= 12 ? 'PM' : 'AM';
    return '$hour:$minute $period';
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black26,
        borderRadius: BorderRadius.circular(16),
      ),
      child: TabBar(
        labelPadding: const EdgeInsets.symmetric(horizontal: 8),
        controller: _tabController,
        indicatorSize: TabBarIndicatorSize.tab,
        indicatorPadding: const EdgeInsets.symmetric(horizontal: 8),
        indicator: BoxDecoration(
          color: widget.accentColor,
          borderRadius: BorderRadius.circular(8),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white70,
        tabs: const [
          Tab(text: 'Date'),
          Tab(text: 'Time'),
        ],
      ),
    );
  }

  Widget _buildDateContent() {
    return Padding(
      padding: const EdgeInsets.only(top: 20),
      child: _buildCalendar(),
    );
  }

  Widget _buildCalendar() {
    // Simplified calendar view for the bottom sheet
    return Column(
      children: [
        _buildMonthSelector(),
        const SizedBox(height: 16),
        Expanded(
          child: _buildDaysGrid(),
        ),
      ],
    );
  }

  Widget _buildMonthSelector() {
    final now = DateTime.now();
    final currentMonth =
        DateTime(_selectedDateTime.year, _selectedDateTime.month);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        IconButton(
          onPressed: () {
            setState(() {
              _selectedDateTime = DateTime(
                _selectedDateTime.year,
                _selectedDateTime.month - 1,
                _selectedDateTime.day,
                _selectedDateTime.hour,
                _selectedDateTime.minute,
              );
            });
          },
          icon: Icon(Icons.chevron_left, color: widget.accentColor),
        ),
        Text(
          '${_getMonthName(_selectedDateTime.month)} ${_selectedDateTime.year}',
          style: GoogleFonts.dmSans(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        IconButton(
          onPressed: () {
            setState(() {
              _selectedDateTime = DateTime(
                _selectedDateTime.year,
                _selectedDateTime.month + 1,
                _selectedDateTime.day,
                _selectedDateTime.hour,
                _selectedDateTime.minute,
              );
            });
          },
          icon: Icon(Icons.chevron_right, color: widget.accentColor),
        ),
      ],
    );
  }

  Widget _buildDaysGrid() {
    final daysInMonth =
        DateTime(_selectedDateTime.year, _selectedDateTime.month + 1, 0).day;
    final firstDayOfMonth =
        DateTime(_selectedDateTime.year, _selectedDateTime.month, 1);
    final firstWeekdayOfMonth =
        firstDayOfMonth.weekday % 7; // 0 = Sunday, 6 = Saturday

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Weekday headers
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _weekdayHeader('S'),
            _weekdayHeader('M'),
            _weekdayHeader('T'),
            _weekdayHeader('W'),
            _weekdayHeader('T'),
            _weekdayHeader('F'),
            _weekdayHeader('S'),
          ],
        ),
        const SizedBox(height: 8),
        // Use a fixed height container for the grid
        SizedBox(
          height: 220, // Fixed height for the grid
          child: GridView.builder(
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 7,
              childAspectRatio: 1.2,
              mainAxisSpacing: 4,
              crossAxisSpacing: 4,
            ),
            itemCount: 42, // 6 rows of 7 days
            itemBuilder: (context, index) {
              final dayOffset = index - firstWeekdayOfMonth;
              final day = dayOffset + 1;

              if (dayOffset < 0 || day > daysInMonth) {
                return const SizedBox(); // Empty cell
              }

              final date = DateTime(
                  _selectedDateTime.year, _selectedDateTime.month, day);
              final isSelected = date.year == _selectedDateTime.year &&
                  date.month == _selectedDateTime.month &&
                  date.day == _selectedDateTime.day;
              final isToday = _isToday(date);

              // Check if date is within allowed range
              final isDisabled = (widget.firstDate != null &&
                      date.isBefore(widget.firstDate!)) ||
                  (widget.lastDate != null && date.isAfter(widget.lastDate!));

              return GestureDetector(
                onTap: isDisabled
                    ? null
                    : () {
                        setState(() {
                          _selectedDateTime = DateTime(
                            date.year,
                            date.month,
                            date.day,
                            _selectedDateTime.hour,
                            _selectedDateTime.minute,
                          );
                        });

                        // Automatically switch to time tab after date selection
                        _tabController.animateTo(1);
                      },
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  margin: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? widget.accentColor
                        : isToday
                            ? widget.accentColor.withOpacity(0.1)
                            : Colors.transparent,
                    borderRadius: BorderRadius.circular(8),
                    border: isToday && !isSelected
                        ? Border.all(color: widget.accentColor, width: 1)
                        : null,
                  ),
                  child: Center(
                    child: Text(
                      day.toString(),
                      style: GoogleFonts.dmSans(
                        color: isDisabled
                            ? Colors.grey.withOpacity(0.5)
                            : isSelected
                                ? Colors.white
                                : Colors.white.withOpacity(0.9),
                        fontWeight: isSelected || isToday
                            ? FontWeight.bold
                            : FontWeight.normal,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _weekdayHeader(String text) {
    return Text(
      text,
      style: GoogleFonts.dmSans(
        color: Colors.white.withOpacity(0.7),
        fontSize: 14,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  Widget _buildTimeContent() {
    return Padding(
      padding: const EdgeInsets.only(top: 20),
      child: Column(
        children: [
          _buildTimeSelector(),
        ],
      ),
    );
  }

  Widget _buildTimeSelector() {
    return Column(
      children: [
        _buildHourMinuteSelector(),
        const SizedBox(height: 10),
        _buildAmPmSelector(),
      ],
    );
  }

  Widget _buildHourMinuteSelector() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Hour selector
        Expanded(
          child: Align(
            alignment: Alignment.centerRight,
            child: _buildTimeUnit(
              value: _selectedDateTime.hour > 12
                  ? _selectedDateTime.hour - 12
                  : _selectedDateTime.hour == 0
                      ? 12
                      : _selectedDateTime.hour,
              label: 'Hour',
              onIncrement: () {
                setState(() {
                  final newHour = (_selectedDateTime.hour + 1) % 24;
                  _selectedDateTime = DateTime(
                    _selectedDateTime.year,
                    _selectedDateTime.month,
                    _selectedDateTime.day,
                    newHour,
                    _selectedDateTime.minute,
                  );
                });
              },
              onDecrement: () {
                setState(() {
                  final newHour = (_selectedDateTime.hour - 1 + 24) % 24;
                  _selectedDateTime = DateTime(
                    _selectedDateTime.year,
                    _selectedDateTime.month,
                    _selectedDateTime.day,
                    newHour,
                    _selectedDateTime.minute,
                  );
                });
              },
            ),
          ),
        ),
        // Separator
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            ':',
            style: GoogleFonts.dmSans(
              fontSize: 36,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
        // Minute selector
        Expanded(
          child: Align(
            alignment: Alignment.centerLeft,
            child: _buildTimeUnit(
              value: _selectedDateTime.minute,
              label: 'Minute',
              onIncrement: () {
                setState(() {
                  final newMinute = (_selectedDateTime.minute + 1) % 60;
                  _selectedDateTime = DateTime(
                    _selectedDateTime.year,
                    _selectedDateTime.month,
                    _selectedDateTime.day,
                    _selectedDateTime.hour,
                    newMinute,
                  );
                });
              },
              onDecrement: () {
                setState(() {
                  final newMinute = (_selectedDateTime.minute - 1 + 60) % 60;
                  _selectedDateTime = DateTime(
                    _selectedDateTime.year,
                    _selectedDateTime.month,
                    _selectedDateTime.day,
                    _selectedDateTime.hour,
                    newMinute,
                  );
                });
              },
              format: (value) => value.toString().padLeft(2, '0'),
              step: 1,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTimeUnit({
    required int value,
    required String label,
    required VoidCallback onIncrement,
    required VoidCallback onDecrement,
    String Function(int)? format,
    int step = 1,
  }) {
    double dragStartDy = 0;
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Up arrow (tappable)
        GestureDetector(
          onTap: () {
            HapticFeedback.selectionClick();
            onIncrement();
          },
          child: Icon(Icons.keyboard_arrow_up, color: Colors.white, size: 28),
        ),
        const SizedBox(height: 4),
        GestureDetector(
          onVerticalDragStart: (details) {
            dragStartDy = details.localPosition.dy;
          },
          onVerticalDragUpdate: (details) {
            final dragDistance = details.localPosition.dy - dragStartDy;
            if (dragDistance <= -40) {
              HapticFeedback.selectionClick();
              onIncrement();
              dragStartDy = details.localPosition.dy;
            } else if (dragDistance >= 40) {
              HapticFeedback.selectionClick();
              onDecrement();
              dragStartDy = details.localPosition.dy;
            }
          },
          child: Container(
            width: 70,
            height: 70,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Center(
              child: Text(
                format != null ? format(value) : value.toString(),
                style: GoogleFonts.dmSans(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 4),
        // Down arrow (tappable)
        GestureDetector(
          onTap: () {
            HapticFeedback.selectionClick();
            onDecrement();
          },
          child: Icon(Icons.keyboard_arrow_down, color: Colors.white, size: 28),
        ),
        Text(
          label,
          style: GoogleFonts.dmSans(
            fontSize: 14,
            color: Colors.white.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildAmPmSelector() {
    final isAm = _selectedDateTime.hour < 12;

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildAmPmButton(
          text: 'AM',
          isSelected: isAm,
          onTap: () {
            if (!isAm) {
              setState(() {
                _selectedDateTime = DateTime(
                  _selectedDateTime.year,
                  _selectedDateTime.month,
                  _selectedDateTime.day,
                  _selectedDateTime.hour - 12,
                  _selectedDateTime.minute,
                );
              });
            }
          },
        ),
        const SizedBox(width: 16),
        _buildAmPmButton(
          text: 'PM',
          isSelected: !isAm,
          onTap: () {
            if (isAm) {
              setState(() {
                _selectedDateTime = DateTime(
                  _selectedDateTime.year,
                  _selectedDateTime.month,
                  _selectedDateTime.day,
                  _selectedDateTime.hour + 12,
                  _selectedDateTime.minute,
                );
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildAmPmButton({
    required String text,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 80,
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color:
              isSelected ? widget.accentColor : Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Center(
          child: Text(
            text,
            style: GoogleFonts.dmSans(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isSelected ? Colors.white : Colors.white.withOpacity(0.7),
            ),
          ),
        ),
      ),
    );
  }



  Widget _buildBottomButtons() {
    return Padding(
      padding: const EdgeInsets.only(top: 24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton(
            onPressed: () => GoRouter.of(context).pop(),
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: Text(
              'Cancel',
              style: GoogleFonts.dmSans(
                color: Colors.white.withOpacity(0.7),
                fontSize: 16,
              ),
            ),
          ),
          const SizedBox(width: 16),
          ElevatedButton(
            onPressed: () {
              widget.onDateTimeSelected(_selectedDateTime);
              GoRouter.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.accentColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(
              'Confirm',
              style: GoogleFonts.dmSans(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
