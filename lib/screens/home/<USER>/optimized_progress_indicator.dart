import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';

import '../../../models/fasting_day.dart';
import '../../../painters/plasma_effect_painter.dart';
import '../../../utils/color_utils.dart';
import '../../../utils/text_utils.dart';
import '../../../utils/time_utils.dart';

class OptimizedProgressIndicator extends StatelessWidget {
  final FastingDay? fastingDay;
  final Animation<double> progressAnimation;
  final Animation<double> pulseAnimation;
  final AnimationController plasmaController;

  const OptimizedProgressIndicator({
    Key? key,
    required this.fastingDay,
    required this.progressAnimation,
    required this.pulseAnimation,
    required this.plasmaController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final progress = calculateProgress(fastingDay);
    final isActive = fastingDay?.isActive ?? false;

    return RepaintBoundary(
      // Isolate repaints to this widget
      child: Center(
        child: Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: const Color(0xFF1A1A1A),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Only show plasma effect if actively fasting
              if (isActive)
                AnimatedBuilder(
                  animation: plasmaController,
                  builder: (context, child) {
                    return RepaintBoundary(
                      child: CustomPaint(
                        painter: OptimizedPlasmaEffectPainter(
                          animation: plasmaController,
                          baseColor: getProgressColor(progress),
                        ),
                        size: const Size(280, 280),
                      ),
                    );
                  },
                ),

              // Progress indicator
              AnimatedBuilder(
                animation: Listenable.merge([
                  progressAnimation,
                  pulseAnimation,
                ]),
                builder: (context, child) {
                  final pulseScale = isActive
                      ? 1.0 + (pulseAnimation.value * 0.03)
                      : 1.0;

                  return Transform.scale(
                    scale: pulseScale,
                    child: CircularPercentIndicator(
                      radius: 140.0,
                      lineWidth: 12.0,
                      percent: progressAnimation.value,
                      center: _buildProgressCenter(fastingDay, isActive),
                      animateFromLastPercent: true,
                      rotateLinearGradient: true,
                      linearGradient: LinearGradient(
                        colors: [
                          getAnimatedColor(progressAnimation.value, 0.5),
                          getProgressColor(progress),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      backgroundColor: Colors.white12,
                      circularStrokeCap: CircularStrokeCap.round,
                      animation: true,
                      animationDuration: 500,
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProgressCenter(FastingDay? fastingDay, bool isActive) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          getRemainingTime(fastingDay),
          style: GoogleFonts.dmSans(
            color: Colors.white,
            fontSize: 22,
            fontWeight: FontWeight.bold,
          ),
        ),
        if (isActive)
          Text(
            _getNextFastingStage(
              getStartDateTime(
                fastingDay,
              ).difference(DateTime.now()).abs().inHours,
            ),
            style: GoogleFonts.dmSans(color: Colors.white70, fontSize: 14),
          ),
      ],
    );
  }

  String _getNextFastingStage(int elapsedHours) {
    for (var stage in fastingStages) {
      if (stage['hours'] > elapsedHours) {
        final hoursUntil = stage['hours'] - elapsedHours;
        return "${stage['name']} in ${hoursUntil}h";
      }
    }
    return "Maximum benefits active";
  }
}
