import 'dart:async';
import 'dart:math';
import 'dart:ui';
import 'package:fasttime/blocs/fasting/fasting_bloc.dart';
import 'package:fasttime/blocs/fasting/fasting_event.dart';
import 'package:fasttime/blocs/fasting/fasting_state.dart';
import 'package:fasttime/blocs/settings/settings_event.dart';
import 'package:fasttime/dialogs/time_selection_dialog.dart';
import 'package:fasttime/dialogs/fasting_summary_dialog.dart';
import 'package:fasttime/dialogs/protocol_selection_dialog.dart';
import 'package:fasttime/models/fasting_day.dart';
import 'package:fasttime/models/fasting_protocol.dart';
import 'package:fasttime/painters/plasma_effect_painter.dart';
import 'package:fasttime/route/router_constants.dart';
import 'package:fasttime/screens/home/<USER>/modern_date_time_picker.dart';
import 'package:fasttime/utils/size_config.dart';
import 'package:fasttime/utils/text_utils.dart';
import 'package:fasttime/utils/time_utils.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fasttime/widgets/bottom_nav.dart';
import 'package:rive/rive.dart' as rive;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:fasttime/blocs/settings/settings_bloc.dart';
import 'package:fasttime/blocs/settings/settings_state.dart';
import 'package:fasttime/utils/subscription_manager.dart';
import 'package:fasttime/services/error_service.dart';

import '../../utils/color_utils.dart';
class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with TickerProviderStateMixin {
  Timer? _timer;
  late AnimationController _progressController;
  late AnimationController _pulseController;
  late AnimationController _waveController;
  late AnimationController _headerFadeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _progressAnimation;
  late AnimationController _plasmaController;
  late AnimationController _particleController;
  final _subscriptions = SubscriptionManager();

  @override
  void initState() {
    super.initState();

    // Initialize controllers
    _waveController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 10),
    )..repeat();

    _plasmaController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 8),
    )..repeat();

    _particleController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 4),
    )..repeat();

    _headerFadeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    )..forward();

    // Initialize progress controller and animation
    _progressController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _pulseController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    )..repeat(reverse: true);

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));

    // Initialize fasting day
    _initializeFastingDay();

    _setupProgressTimer();
    // Listen for changes in the FastingBloc state
    _subscriptions.add(context.read<FastingBloc>().stream.listen((state) {
      if (mounted) {
        setState(() {
          final fastingDay = state.currentDay;
          _progressAnimation =
              AlwaysStoppedAnimation(calculateProgress(fastingDay));
        });
      }
    }));
  }

  @override
  void dispose() {
    _waveController.dispose();
    _plasmaController.dispose();
    _particleController.dispose();
    _headerFadeController.dispose();
    _progressController.dispose();
    _pulseController.dispose();
    _timer?.cancel();
    _subscriptions.cancelAll();
    super.dispose();
  }

  void _setupProgressTimer() {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      if (mounted) {
        setState(() {
          final fastingDay = context.read<FastingBloc>().state.currentDay;
          final newProgress = calculateProgress(fastingDay);
          _progressAnimation = AlwaysStoppedAnimation<double>(newProgress);
        });
      }
    });
  }

  void _initializeFastingDay() {
    final now = DateTime.now();
    final fastingDay = context.read<FastingBloc>().state.currentDay;

    if (fastingDay == null) {
      final today = DateTime(now.year, now.month, now.day);
      context.read<FastingBloc>().add(LoadFastingDay(today));
    }
  }

  // Get fasting window start time as DateTime
  DateTime _getStartDateTime(FastingDay? fastingDay) {
    if (fastingDay == null || fastingDay.startTime == '--:--') {
      return DateTime.now();
    }

    final timeComponents = fastingDay.startTime.split(':');
    final hours = int.parse(timeComponents[0]);
    final minutes = int.parse(timeComponents[1]);

    // If there's a specific start date recorded, use that instead
    if (fastingDay.startDate != null) {
      return fastingDay.startDate!;
    }

    final now = DateTime.now();
    var startDate = DateTime(
      fastingDay.date.year,
      fastingDay.date.month,
      fastingDay.date.day,
      hours,
      minutes,
    );

    // If the start time is in the future of the current day,
    // use the previous day's date for cases where fast was started yesterday
    if (!fastingDay.isActive && startDate.isAfter(now)) {
      startDate = startDate.subtract(const Duration(days: 1));
    }

    return startDate;
  }

  // Get fasting window end time as DateTime based on protocol
  DateTime _getEndDateTime(FastingDay? fastingDay) {
    if (fastingDay == null || fastingDay.endTime == '--:--') {
      return DateTime.now();
    }

    // If there's a recorded end date (for completed fasts), use that
    if (fastingDay.endDate != null && fastingDay.completed) {
      return fastingDay.endDate!;
    }

    final timeComponents = fastingDay.endTime.split(':');
    final hours = int.parse(timeComponents[0]);
    final minutes = int.parse(timeComponents[1]);

    final startDateTime = _getStartDateTime(fastingDay);

    // Get protocol duration
    final protocolHours = int.parse(fastingDay.fastingProtocol.split(':')[0]);

    // Calculate end time based on start time and protocol duration
    var calculatedEndDate = startDateTime.add(Duration(hours: protocolHours));

    // If specific end time is set, use that
    var endDate = DateTime(
      fastingDay.date.year,
      fastingDay.date.month,
      fastingDay.date.day,
      hours,
      minutes,
    );

    // Adjust date if end time is on the next day
    while (endDate.isBefore(startDateTime)) {
      endDate = endDate.add(const Duration(days: 1));
    }

    // If end time hasn't been manually set yet, use calculated end time
    if (fastingDay.endTime == '--:--' ||
        _timeToString(TimeOfDay(
            hour: calculatedEndDate.hour,
            minute: calculatedEndDate.minute)) ==
            fastingDay.endTime) {
      return calculatedEndDate;
    }

    return endDate;
  }

  String _timeToString(TimeOfDay timeOfDay) {
    return '${timeOfDay.hour.toString().padLeft(2, '0')}:${timeOfDay.minute.toString().padLeft(2, '0')}';
  }

  String _formatTimeOfDay(String? timeStr) {
    if (timeStr == null || timeStr == '--:--') return '--:--';

    final parts = timeStr.split(':');
    if (parts.length != 2) return '--:--';

    try {
      return TimeOfDay(hour: int.parse(parts[0]), minute: int.parse(parts[1]))
          .format(context);
    } catch (e) {
      return '--:--';
    }
  }

  String getSubtitleText(FastingDay? fastingDay) {
    if (fastingDay == null || fastingDay.startTime == '--:--') {
      return "Set your fasting window";
    }

    final now = DateTime.now();
    final startDateTime = getStartDateTime(fastingDay);
    final endDateTime = getEndDateTime(fastingDay);
    final targetDuration =
    Duration(hours: int.parse(fastingDay.fastingProtocol.split(':')[0]));

    // If fast is scheduled for future
    if (now.isBefore(startDateTime)) {
      return "Starts at ${DateFormat('h:mm a').format(startDateTime)}";
    }

    if (fastingDay.completed) {
      final actualDuration = fastingDay.endDate != null
          ? fastingDay.endDate!.difference(startDateTime)
          : endDateTime.difference(startDateTime);

      // Check if completed within last 5 minutes
      if (fastingDay.endDate != null &&
          now.difference(fastingDay.endDate!).inMinutes <= 5) {
        final overtime = actualDuration - targetDuration;
        if (overtime.inMinutes > 0) {
          return "Great job! Fasted extra ${formatDuration(overtime)}";
        }
        return "Great job!";
      }

      final overtime = actualDuration - targetDuration;
      if (overtime.inMinutes > 0) {
        return "Last fast completed ${formatTimeAgo(fastingDay.endDate!)} (+${formatDuration(overtime)})";
      }
      return "Last fast completed ${formatTimeAgo(fastingDay.endDate!)}";
    }

    if (fastingDay.isActive && !now.isBefore(startDateTime)) {
      final elapsed = now.difference(startDateTime);
      final fastingStage = _getCurrentFastingStage(elapsed.inHours);

      final overtime = elapsed - targetDuration;
      if (overtime.inMinutes > 0) {
        return "In ${fastingStage['name']}: ${elapsed.inHours}h ${elapsed.inMinutes % 60}m (+${overtime.inHours}h ${overtime.inMinutes % 60}m)";
      }
      return "In ${fastingStage['name']}: ${elapsed.inHours}h ${elapsed.inMinutes % 60}m";
    } else if (now.isAfter(endDateTime)) {
      return "Fast window ended";
    } else {
      return "Ready to start";
    }
  }

  // Determine the current fasting stage based on elapsed hours
  Map<String, dynamic> _getCurrentFastingStage(int elapsedHours) {
    // Don't show stages if fast hasn't started yet
    final now = DateTime.now();
    final fastingDay = context.read<FastingBloc>().state.currentDay;
    final startDateTime = _getStartDateTime(fastingDay);

    if (now.isBefore(startDateTime)) {
      return fastingStages[0];
    }

    for (int i = fastingStages.length - 1; i >= 0; i--) {
      if (elapsedHours >= fastingStages[i]['hours']) {
        return fastingStages[i];
      }
    }
    return fastingStages[0];
  }

  String _getNextFastingStage(int elapsedHours) {
    for (var stage in fastingStages) {
      if (stage['hours'] > elapsedHours) {
        final hoursUntil = stage['hours'] - elapsedHours;
        return "${stage['name']} in ${hoursUntil}h";
      }
    }
    return "Maximum benefits active";
  }

  void _updateRoutine(String type, double value) {
    final bloc = context.read<FastingBloc>();
    bloc.add(UpdateDailyRoutine(
      date: DateTime.now(),
      waterIntake: type == 'Water' ? value : null,
      steps: type == 'Steps' ? value.toInt() : null,
      exercise: type == 'Exercise' ? value : null,
    ));
  }

  // Find protocol by ID
  FastingProtocol _getProtocolById(String id) {
    return availableProtocols.firstWhere(
          (protocol) => protocol.id == id,
      orElse: () =>
      availableProtocols[0], // Default to first protocol if not found
    );
  }

  // Update start time and calculate end time based on selected protocol
  void _updateStartTime(DateTime startDateTime, TimeOfDay time,
      {String? protocolId}) {
    final formattedTime = _timeToString(time);

    // Get current protocol or default to 16:8
    final currentState = context.read<FastingBloc>().state;
    final currentProtocolId =
        protocolId ?? (currentState.currentDay?.fastingProtocol ?? '16:8');

    // Get the protocol hours
    final protocolHours = int.parse(currentProtocolId.split(':')[0]);

    // Calculate end time based on protocol duration
    final endDateTime = DateTime(
      startDateTime.year,
      startDateTime.month,
      startDateTime.day,
      startDateTime.hour,
      startDateTime.minute,
    ).add(Duration(hours: protocolHours));

    final endTime = TimeOfDay(
      hour: endDateTime.hour,
      minute: endDateTime.minute,
    );
    final formattedEndTime = _timeToString(endTime);

    // Start fasting with the new times and selected protocol
    context.read<FastingBloc>().add(UpdateFastingWindow(
      date: startDateTime,
      startTime: formattedTime,
      endTime: formattedEndTime,
      isActive: true, // Set to true to start immediately
      completed: false,
      startDate: startDateTime,
      endDate: null, // End date should be null until completed
      fastingProtocol: currentProtocolId,
    ));
  }

  void _updateEndTime(DateTime endDateTime, TimeOfDay time) {
    final formattedTime = _timeToString(time);
    final currentState = context.read<FastingBloc>().state;
    final startDateTime = _getStartDateTime(currentState.currentDay);

    // Update fasting window with new end time
    context.read<FastingBloc>().add(UpdateFastingWindow(
      date: currentState.currentDay?.date ?? DateTime.now(),
      startTime: currentState.currentDay?.startTime ?? '--:--',
      endTime: formattedTime,
      isActive: true,
      completed: false,
      startDate: startDateTime,
      endDate: null, // End date should be null until completed
      fastingProtocol: currentState.currentDay?.fastingProtocol ?? '16:8',
    ));
  }

  // Update fasting protocol
  void _updateProtocol(String protocolId) {
    final currentState = context.read<FastingBloc>().state;
    final fastingDay = currentState.currentDay;

    if (fastingDay == null) return;

    final startDateTime = _getStartDateTime(fastingDay);

    // Get the protocol hours
    final protocolHours = int.parse(protocolId.split(':')[0]);

    // Calculate end time based on protocol duration
    final endDateTime = startDateTime.add(Duration(hours: protocolHours));
    final endTime = TimeOfDay(
      hour: endDateTime.hour,
      minute: endDateTime.minute,
    );
    final formattedEndTime = _timeToString(endTime);

    // Update with new protocol and recalculated end time
    context.read<FastingBloc>().add(UpdateFastingWindow(
      date: fastingDay.date,
      startTime: fastingDay.startTime,
      endTime: formattedEndTime, // Updated end time based on new protocol
      isActive: fastingDay.isActive,
      completed: fastingDay.completed,
      startDate: startDateTime,
      endDate: fastingDay.endDate,
      fastingProtocol: protocolId, // New protocol
    ));

    // Update the settings bloc with the new protocol
    context.read<SettingsBloc>().add(UpdateFastingProtocol(protocolId));
    context.read<SettingsBloc>().add(SaveAllSettings());

    // Force UI refresh
    setState(() {});
  }

  Future<void> _showProtocolSelector() async {
    final currentState = context.read<FastingBloc>().state;
    final currentProtocolId =
        currentState.currentDay?.fastingProtocol ?? '16:8';

    final result = await ProtocolSelectionDialog.show(
      context: context,
      protocols: availableProtocols,
      selectedProtocolId: currentProtocolId,
    );

    if (result != null) {
      // Update the protocol with the selected one
      _updateProtocol(result);

      // Show confirmation
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Protocol updated to $result',
            style: GoogleFonts.dmSans(color: Colors.white),
          ),
          backgroundColor: const Color(0xFF6B4EFF),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }

  Future<void> _showDateTimePicker(
      FastingDay? fastingDay, bool isStartTime) async {
    DateTime currentDateTime;

    if (fastingDay == null || fastingDay.startTime == '--:--') {
      currentDateTime = DateTime.now();
    } else {
      if (isStartTime) {
        currentDateTime = _getStartDateTime(fastingDay);
      } else {
        currentDateTime = _getEndDateTime(fastingDay);
      }
    }

    final result = await ModernDateTimePicker.show(
      context: context,
      initialDateTime: currentDateTime,
      label: isStartTime ? 'Start Time' : 'End Time',
      firstDate: DateTime.now().subtract(const Duration(days: 7)),
      lastDate: DateTime.now().add(const Duration(days: 1)),
    );

    if (result != null) {
      final time = TimeOfDay.fromDateTime(result);

      if (isStartTime) {
        _updateStartTime(result, time);
      } else {
        _updateEndTime(result, time);
      }
    }
  }

  void _handleFastingAction() {
    final now = DateTime.now();
    final fastingDay = context.read<FastingBloc>().state.currentDay;

    if (fastingDay == null || !fastingDay.isActive) {
      // Starting a new fast
      if (fastingDay == null || fastingDay.startTime == '--:--') {
        _showDateTimePicker(fastingDay, true);
        return;
      }

      final startDateTime = _getStartDateTime(fastingDay);

      // Check if the fast is scheduled for the future
      if (startDateTime.isAfter(now)) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            backgroundColor: Colors.black54,
            content: Text(
              'Fast is scheduled to start at ${DateFormat('h:mm a').format(startDateTime)}',
              style: GoogleFonts.dmSans(color: Colors.white),
            ),
            duration: const Duration(seconds: 3),
          ),
        );
        return;
      }

      // Start new fast
      final endDateTime = _getEndDateTime(fastingDay);
      context.read<FastingBloc>().add(
        UpdateFastingWindow(
          date: now,
          startTime: _timeToString(TimeOfDay(
              hour: startDateTime.hour, minute: startDateTime.minute)),
          endTime: _timeToString(TimeOfDay(
              hour: endDateTime.hour, minute: endDateTime.minute)),
          isActive: true,
          completed: false,
          startDate: startDateTime,
          endDate: null,
          actualFastingMinutes: null,
          fastingProtocol: fastingDay.fastingProtocol,
        ),
      );
    } else {
      // Ending a fast
      final startDateTime = _getStartDateTime(fastingDay);
      final actualDuration = now.difference(startDateTime).inMinutes;

      context.read<FastingBloc>().add(
        UpdateFastingWindow(
          date: fastingDay.date,
          startTime: fastingDay.startTime,
          endTime: _timeToString(TimeOfDay.now()),
          isActive: false,
          completed: true,
          startDate: startDateTime,
          endDate: now,
          actualFastingMinutes: actualDuration,
          fastingProtocol: fastingDay.fastingProtocol,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    SizeConfig.init(context);
    return BlocListener<FastingBloc, FastingState>(
      listener: (context, state) {
        if (state.error != null) {
          print('FastingBloc error: ${state.error}');
          ErrorService.showErrorBanner(
            context,
            message: state.error!,
          );
        }
      },
      child: BlocBuilder<FastingBloc, FastingState>(
        builder: (context, state) {
          if (state.isLoading) {
            return const Scaffold(
              backgroundColor: Color(0xFF121212),
              body:
              Center(child: CircularProgressIndicator(color: Colors.white)),
            );
          }

          final fastingDay = state.currentDay;

          return Scaffold(
            backgroundColor: Color(0xFF121212),
            body: SafeArea(
              child: Stack(
                children: [
                  // Animated background waves
                  Positioned.fill(
                      child: rive.RiveAnimation.asset(
                        'assets/bg.riv',
                        fit: BoxFit.cover,
                        alignment: Alignment.center,
                        speedMultiplier: 0.1,
                      )),
                  SingleChildScrollView(
                    physics: const BouncingScrollPhysics(),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20.0, vertical: 16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          FadeTransition(
                            opacity: _headerFadeController,
                            child: _buildHeader(fastingDay),
                          ),
                          SizeConfig.h24(context),
                          _buildProgressIndicator(fastingDay),
                          _buildActionButton(fastingDay),
                          SizeConfig.h24(context),
                          _buildTimingCards(fastingDay),
                          SizeConfig.h24(context),
                          _buildRoutineTracker(fastingDay),
                          SizeConfig.h16(context),
                          _buildFastingStagesInfo(fastingDay),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            bottomNavigationBar: BottomNav(currentRoute: RouteConstants.home),
          );
        },
      ),
    );
  }

  Widget _buildHeader(FastingDay? fastingDay) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'FastTime',
              style: GoogleFonts.dmSans(
                color: Colors.white,
                fontSize: 28,
                fontWeight: FontWeight.bold,
                letterSpacing: 0.5,
              ),
            ),
            SizedBox(height: 4),
            Text(
              getSubtitleText(fastingDay),
              style: GoogleFonts.dmSans(
                color: Colors.white70,
                fontSize: 14,
              ),
            ),
          ],
        ),
        IconButton(
          icon: Icon(Icons.settings_outlined, color: Colors.white70),
          onPressed: () {
            context.pushNamed(RouteConstants.settings);
          },
        ),
      ],
    );
  }

  Widget _buildProgressIndicator(FastingDay? fastingDay) {
    final progress = calculateProgress(fastingDay);
    final isActive = fastingDay?.isActive ?? false;
    Color progressColor = Colors.blue;

    // Get current fasting stage for color if active
    if (isActive && fastingDay != null) {
      final now = DateTime.now();
      final startDateTime = _getStartDateTime(fastingDay);
      final elapsed = now.difference(startDateTime);
      final stage = _getCurrentFastingStage(elapsed.inHours);
      progressColor = stage['color'] as Color;
    }
    final animatedColor = getAnimatedColor(
      _progressAnimation.value,
      sin(_pulseController.value * pi) * 0.5 + 0.5,
    );

    return Center(
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: const Color(0xFF1A1A1A),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: animatedColor.withOpacity(0.5),
                      blurRadius: 10,
                      spreadRadius: 10,
                    ),
                  ],
                ),
              ),
            ),
            // Plasma effect
            AnimatedBuilder(
              animation: _plasmaController,
              builder: (context, child) {
                return CustomPaint(
                  painter: PlasmaEffectPainter(
                    animation: _plasmaController,
                    baseColor: getProgressColor(progress),
                  ),
                  size: const Size(280, 280),
                );
              },
            ),
            AnimatedBuilder(
              animation:
              Listenable.merge([_progressAnimation, _pulseAnimation]),
              builder: (context, child) {
                // Calculate scale effect based on pulse animation
                final pulseScale = 1.0 + (_pulseAnimation.value * 0.03);

                return Transform.scale(
                  scale: isActive ? pulseScale : 1.0,
                  child: CircularPercentIndicator(
                    radius: 140.0,
                    lineWidth: 12.0,
                    percent: _progressAnimation.value,
                    center: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          getRemainingTime(fastingDay),
                          style: GoogleFonts.dmSans(
                            color: Colors.white,
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (isActive)
                          Text(
                            _getNextFastingStage(_getStartDateTime(fastingDay)
                                .difference(DateTime.now())
                                .abs()
                                .inHours),
                            style: GoogleFonts.dmSans(
                              color: Colors.white70,
                              fontSize: 14,
                            ),
                          ),
                      ],
                    ),
                    animateFromLastPercent: true,
                    rotateLinearGradient: true,
                    linearGradient: LinearGradient(
                      colors: [
                        animatedColor,
                        progressColor,
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    // progressColor: progressColor,
                    backgroundColor: Colors.white12,
                    circularStrokeCap: CircularStrokeCap.round,
                    animation: true,
                    animationDuration: 500,
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(FastingDay? fastingDay) {
    final isActive = fastingDay?.isActive ?? false;
    final buttonText = isActive ? 'End Fast' : 'Start Fast';
    final buttonColor = isActive ? Colors.red : Colors.green;

    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 24.0),
        child: ElevatedButton(
          onPressed: () => _handleFastingAction(),
          style: ElevatedButton.styleFrom(
            backgroundColor: buttonColor,
            foregroundColor: Colors.white,
            padding: EdgeInsets.symmetric(horizontal: 32, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(30),
            ),
            elevation: 5,
          ),
          child: Text(
            buttonText,
            style:
            GoogleFonts.dmSans(fontSize: 18, fontWeight: FontWeight.w500),
          ),
        ),
      ),
    );
  }

  Widget _buildTimingCards(FastingDay? fastingDay) {
    return Row(
      children: [
        Expanded(
          child: _buildTimeCard(
            'Start',
            fastingDay?.startTime ?? '--:--',
                () => _showDateTimePicker(fastingDay, true),
          ),
        ),
        SizedBox(width: MediaQuery.of(context).size.width * 0.01),
        Expanded(
          child: _buildTimeCard(
            'End',
            fastingDay?.endTime ?? '--:--',
                () => _showDateTimePicker(fastingDay, false),
          ),
        ),
        SizedBox(width: MediaQuery.of(context).size.width * 0.01),
        Expanded(
          child: _buildProtocolCard(
            'Protocol',
            fastingDay?.fastingProtocol ?? '16:8',
            _showProtocolSelector,
          ),
        ),
      ],
    );
  }

  Widget _buildTimeCard(String title, String value, VoidCallback onTap) {
    final screenSize = MediaQuery.of(context).size;
    final padding = screenSize.width * 0.04;
    final fontSize = screenSize.width * 0.04;
    final smallFontSize = screenSize.width * 0.035;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(padding),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.05),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 8,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Text(
              title,
              style: GoogleFonts.dmSans(
                color: Colors.white70,
                fontSize: smallFontSize,
              ),
            ),
            SizedBox(height: screenSize.height * 0.01),
            Text(
              _formatTimeOfDay(value),
              style: GoogleFonts.dmSans(
                color: Colors.white,
                fontSize: fontSize,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProtocolCard(String title, String value, VoidCallback onTap) {
    // Get the protocol object to display more info
    final protocol = _getProtocolById(value);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.05),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              title,
              style: GoogleFonts.dmSans(
                color: Colors.white70,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: GoogleFonts.dmSans(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRoutineTracker(FastingDay? fastingDay) {
    final screenSize = MediaQuery.of(context).size;
    final horizontalPadding = screenSize.width * 0.04; // 4% of screen width
    final verticalSpacing = screenSize.height * 0.02; // 2% of screen height
    final cardSpacing = screenSize.width * 0.01; // 3% of screen width

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
          child: Text(
            'Daily Habits',
            style: GoogleFonts.dmSans(
              color: Colors.white,
              fontSize: screenSize.width * 0.045, // Dynamic font size
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        SizedBox(height: verticalSpacing),
        Row(
          children: [
            Expanded(
              child: _buildRoutineCard(
                'Water',
                Icons.water_drop_outlined,
                Colors.blue,
                formatRoutineValue(fastingDay, 'Water'),
                (fastingDay?.waterIntake ?? 0) / 2.0,
                    (value) => _updateRoutine('Water', value * 2.0),
              ),
            ),
            SizedBox(width: cardSpacing),
            Expanded(
              child: _buildRoutineCard(
                'Steps',
                Icons.directions_walk,
                Colors.green,
                formatRoutineValue(fastingDay, 'Steps'),
                (fastingDay?.steps ?? 0) / 10000.0,
                    (value) => _updateRoutine('Steps', value * 10000.0),
              ),
            ),
            SizedBox(width: cardSpacing),
            Expanded(
              child: _buildRoutineCard(
                'Exercise',
                Icons.fitness_center,
                Colors.orange,
                formatRoutineValue(fastingDay, 'Exercise'),
                (fastingDay?.exercise ?? 0) / 2.5,
                    (value) => _updateRoutine('Exercise', value * 2.5),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRoutineCard(
      String title,
      IconData icon,
      Color color,
      String value,
      double progress,
      Function(double) onChanged,
      ) {
    final screenSize = MediaQuery.of(context).size;
    final padding = screenSize.width * 0.03; // 3% of screen width
    final iconSize = screenSize.width * 0.045; // 4.5% of screen width
    final fontSize = screenSize.width * 0.035; // 3.5% of screen width
    final buttonSize = screenSize.width * 0.06; // 6% of screen width
    final verticalSpacing = screenSize.height * 0.01; // 1% of screen height

    return Container(
      padding: EdgeInsets.all(padding),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Icon(icon, color: color, size: iconSize),
              Text(
                title,
                style: GoogleFonts.dmSans(
                  color: Colors.white70,
                  fontSize: fontSize,
                ),
              ),
            ],
          ),
          SizedBox(height: verticalSpacing * 1.2),
          LinearProgressIndicator(
            value: progress.clamp(0.0, 1.0),
            backgroundColor: Color(0xFF444444),
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
          SizedBox(height: verticalSpacing * 0.8),
          Text(
            value,
            style: GoogleFonts.dmSans(
              color: Colors.white,
              fontSize: fontSize,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: verticalSpacing * 0.8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(
                onTap: () => onChanged((progress - 0.1).clamp(0.0, 1.0)),
                child: Container(
                  width: buttonSize,
                  height: buttonSize,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Color(0xFF444444),
                  ),
                  child: Icon(
                    Icons.remove,
                    color: Colors.white,
                    size: buttonSize * 0.7,
                  ),
                ),
              ),
              GestureDetector(
                onTap: () => onChanged((progress + 0.1).clamp(0.0, 1.0)),
                child: Container(
                  width: buttonSize,
                  height: buttonSize,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: color.withOpacity(0.8),
                  ),
                  child: Icon(
                    Icons.add,
                    color: Colors.white,
                    size: buttonSize * 0.7,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFastingStagesInfo(FastingDay? fastingDay) {
    final isActive = fastingDay?.isActive ?? false;
    int currentStageIndex = 0;

    if (isActive && fastingDay != null) {
      final now = DateTime.now();
      final startDateTime = _getStartDateTime(fastingDay);
      final elapsed = now.difference(startDateTime).inHours;

      // Find current stage index
      for (int i = fastingStages.length - 1; i >= 0; i--) {
        if (elapsed >= fastingStages[i]['hours']) {
          currentStageIndex = i;
          break;
        }
      }
    }

    // List of stage icons for visualization
    final List<IconData> stageIcons = [
      Icons.lunch_dining, // Fed State
      Icons.hourglass_empty, // Post-Absorptive
      Icons.trending_down, // Early Fasting
      Icons.local_fire_department, // Fat Burning
      Icons.flash_on, // Ketosis
      Icons.recycling, // Autophagy
      Icons.fitness_center, // Growth Hormone
      Icons.auto_fix_high, // Deep Autophagy
    ];

    return AnimatedOpacity(
      opacity: 1.0,
      duration: Duration(milliseconds: 800),
      child: Container(
        constraints: BoxConstraints(
          minHeight: 100, // Ensure minimum height
        ),
        child: SingleChildScrollView(
          physics: BouncingScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with animation
              stageBuildHeader(isActive, currentStageIndex, stageIcons),

              // Main card with frosted glass effect
              _buildMainCard(
                  isActive, currentStageIndex, stageIcons, fastingDay),
            ],
          ),
        ),
      ),
    );
  }

  Widget stageBuildHeader(
      bool isActive, int currentStageIndex, List<IconData> stageIcons) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: Duration(milliseconds: 800),
      curve: Curves.easeOutQuart,
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 20 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: child,
          ),
        );
      },
      child: Padding(
        padding: const EdgeInsets.only(bottom: 16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Icon(
                  Icons.timer,
                  color: Colors.white,
                  size: 22,
                ),
                SizedBox(width: 8),
                Text(
                  'Fasting Stages',
                  style: GoogleFonts.dmSans(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
            if (isActive)
              AnimatedContainer(
                duration: Duration(milliseconds: 300),
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: fastingStages[currentStageIndex]['color'] as Color,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color:
                      (fastingStages[currentStageIndex]['color'] as Color)
                          .withOpacity(0.3),
                      blurRadius: 8,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      stageIcons[currentStageIndex],
                      color: Colors.white,
                      size: 14,
                    ),
                    SizedBox(width: 4),
                    Text(
                      fastingStages[currentStageIndex]['name'] as String,
                      style: GoogleFonts.dmSans(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildMainCard(bool isActive, int currentStageIndex,
      List<IconData> stageIcons, FastingDay? fastingDay) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(20),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.05),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 15,
                offset: Offset(0, 5),
              ),
            ],
            border: Border.all(
              color: Colors.white.withOpacity(0.1),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Progress timeline
              _buildTimeline(isActive, currentStageIndex, stageIcons),

              // Current stage info if active
              if (isActive)
                _buildCurrentStageCard(
                    currentStageIndex, stageIcons, fastingDay),

              // All stages list
              _buildAllStagesList(isActive, currentStageIndex, stageIcons),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTimeline(
      bool isActive, int currentStageIndex, List<IconData> stageIcons) {
    return Padding(
      padding: const EdgeInsets.only(top: 24, bottom: 6),
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Container(
            height: MediaQuery.of(context).size.height * 0.1,
            child: Stack(
              alignment: Alignment.center,
              children: [
                // Progress timeline with glow
                if (isActive)
                  Positioned(
                    top: 40,
                    child: AnimatedContainer(
                      duration: Duration(milliseconds: 500),
                      width: (constraints.maxWidth - 48) *
                          (currentStageIndex / (fastingStages.length - 1)),
                      height: 6,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.blue,
                            fastingStages[currentStageIndex]['color'] as Color,
                          ],
                        ),
                        borderRadius: BorderRadius.circular(3),
                        boxShadow: [
                          BoxShadow(
                            color: (fastingStages[currentStageIndex]['color']
                            as Color)
                                .withOpacity(0.5),
                            blurRadius: 6,
                            spreadRadius: 1,
                          ),
                        ],
                      ),
                    ),
                  ),

                // Stages markers - horizontal scrollable
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    height: 60,
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      physics: BouncingScrollPhysics(),
                      padding: EdgeInsets.symmetric(horizontal: 24),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: List.generate(fastingStages.length, (i) {
                          final stage = fastingStages[i];
                          final isPastOrCurrent =
                              isActive && i <= currentStageIndex;

                          return Container(
                            width: 60, // Fixed width for each marker
                            child: _buildStageMarker(
                              stage['hours'].toString(),
                              isPastOrCurrent
                                  ? (stage['color'] as Color)
                                  : Colors.grey[700]!,
                              isPastOrCurrent,
                              i == currentStageIndex,
                              stageIcons[i],
                            ),
                          );
                        }),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildCurrentStageCard(int currentStageIndex,
      List<IconData> stageIcons, FastingDay? fastingDay) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.8, end: 1.0),
      duration: Duration(milliseconds: 300),
      curve: Curves.easeOutQuart,
      builder: (context, value, child) {
        return Transform.scale(
          scale: value,
          child: Opacity(
            opacity: value,
            child: child,
          ),
        );
      },
      child: Container(
        margin: EdgeInsets.fromLTRB(16, 0, 16, 16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF353535),
              Color(0xFF252525),
            ],
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: (fastingStages[currentStageIndex]['color'] as Color)
                  .withOpacity(0.15),
              blurRadius: 10,
              spreadRadius: 1,
            ),
          ],
          border: Border.all(
            color: (fastingStages[currentStageIndex]['color'] as Color)
                .withOpacity(0.5),
            width: 1.5,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Animated icon container
                      TweenAnimationBuilder<double>(
                        tween: Tween<double>(begin: 0.0, end: 1.0),
                        duration: Duration(milliseconds: 800),
                        curve: Curves.elasticOut,
                        builder: (context, value, child) {
                          return Transform.scale(
                            scale: value,
                            child: child,
                          );
                        },
                        child: Container(
                          padding: EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: (fastingStages[currentStageIndex]['color']
                            as Color)
                                .withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: (fastingStages[currentStageIndex]
                                ['color'] as Color)
                                    .withOpacity(0.3),
                                blurRadius: 8,
                                spreadRadius: -2,
                              ),
                            ],
                          ),
                          child: Icon(
                            stageIcons[currentStageIndex],
                            color: fastingStages[currentStageIndex]['color']
                            as Color,
                            size: 28,
                          ),
                        ),
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              fastingStages[currentStageIndex]['name']
                              as String,
                              style: GoogleFonts.inter(
                                color: Colors.white,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                letterSpacing: 0.5,
                              ),
                            ),
                            SizedBox(height: 4),
                            Wrap(
                              spacing: 8,
                              children: [
                                Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 3),
                                  decoration: BoxDecoration(
                                    color: (fastingStages[currentStageIndex]
                                    ['color'] as Color)
                                        .withOpacity(0.2),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    '${fastingStages[currentStageIndex]['hours']}+ hours',
                                    style: GoogleFonts.dmSans(
                                      color: fastingStages[currentStageIndex]
                                      ['color'] as Color,
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                if (fastingDay != null)
                                  _buildElapsedBadge(fastingDay),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  Container(
                    padding: EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.05),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      fastingStages[currentStageIndex]['description'] as String,
                      style: GoogleFonts.dmSans(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 14,
                        height: 1.4,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            if (currentStageIndex < fastingStages.length - 1)
              Container(
                margin: EdgeInsets.fromLTRB(16, 0, 16, 16),
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                decoration: BoxDecoration(
                  color: Color(0xFF444444).withOpacity(0.3),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.05),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.upcoming,
                      color: Colors.white70,
                      size: 18,
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Coming up: ${fastingStages[currentStageIndex + 1]['name']}',
                            style: GoogleFonts.dmSans(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'In ${fastingStages[currentStageIndex + 1]['hours'] - fastingStages[currentStageIndex]['hours']} hours',
                            style: GoogleFonts.dmSans(
                              color: Colors.white70,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      width: 36,
                      height: 36,
                      decoration: BoxDecoration(
                        color: (fastingStages[currentStageIndex + 1]['color']
                        as Color)
                            .withOpacity(0.2),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        stageIcons[currentStageIndex + 1],
                        color: fastingStages[currentStageIndex + 1]['color']
                        as Color,
                        size: 20,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildElapsedBadge(FastingDay fastingDay) {
    final now = DateTime.now();
    final startDateTime = _getStartDateTime(fastingDay);
    final elapsed = now.difference(startDateTime);

    if (!fastingDay.isActive) return SizedBox.shrink();

    // Format the duration to show hours and minutes
    final hours = elapsed.inHours;
    final minutes = elapsed.inMinutes % 60;

    return Container(
      margin: EdgeInsets.only(left: 8),
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.timer,
            color: Colors.green,
            size: 12,
          ),
          SizedBox(width: 4),
          Text(
            '${hours}h ${minutes}m',
            style: GoogleFonts.dmSans(
              color: Colors.green,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAllStagesList(
      bool isActive, int currentStageIndex, List<IconData> stageIcons) {
    return Theme(
      data: Theme.of(context).copyWith(
        dividerColor: Colors.transparent,
        unselectedWidgetColor: Colors.white70,
      ),
      child: ExpansionTile(
        tilePadding: EdgeInsets.symmetric(horizontal: 16),
        childrenPadding: EdgeInsets.only(left: 8, right: 8, bottom: 16),
        title: Row(
          children: [
            Icon(
              Icons.list_alt,
              color: Colors.white70,
              size: 18,
            ),
            SizedBox(width: 8),
            Text(
              'View All Stages',
              style: GoogleFonts.dmSans(
                color: Colors.white70,
                fontSize: 14,
              ),
            ),
          ],
        ),
        trailing: Container(
          padding: EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: Color(0xFF444444),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(
            Icons.keyboard_arrow_down,
            color: Colors.white70,
            size: 18,
          ),
        ),
        children: fastingStages.asMap().entries.map((entry) {
          final i = entry.key;
          final stage = entry.value;
          final bool isCurrentStage = isActive && i == currentStageIndex;
          final bool isPastStage = isActive && i < currentStageIndex;
          final bool isFutureStage = isActive && i > currentStageIndex;

          return Container(
            margin: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
            decoration: BoxDecoration(
              color: isCurrentStage
                  ? (stage['color'] as Color).withOpacity(0.15)
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(12),
              border: isCurrentStage
                  ? Border.all(
                  color: (stage['color'] as Color).withOpacity(0.3),
                  width: 1)
                  : null,
            ),
            child: ListTile(
              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              leading: Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isCurrentStage
                      ? (stage['color'] as Color).withOpacity(0.2)
                      : isPastStage
                      ? (stage['color'] as Color).withOpacity(0.8)
                      : Color(0xFF444444),
                  boxShadow: isCurrentStage
                      ? [
                    BoxShadow(
                      color: (stage['color'] as Color).withOpacity(0.3),
                      blurRadius: 8,
                      spreadRadius: 1,
                    ),
                  ]
                      : null,
                ),
                child: Center(
                  child: AnimatedSwitcher(
                    duration: Duration(milliseconds: 300),
                    transitionBuilder:
                        (Widget child, Animation<double> animation) {
                      return ScaleTransition(scale: animation, child: child);
                    },
                    child: isCurrentStage
                        ? Icon(
                      stageIcons[i],
                      key: ValueKey('current-${stageIcons[i]}'),
                      color: stage['color'] as Color,
                      size: 20,
                    )
                        : isPastStage
                        ? Icon(
                      Icons.check_circle,
                      key: ValueKey('past'),
                      color: Colors.white,
                      size: 18,
                    )
                        : Icon(
                      stageIcons[i],
                      key: ValueKey('future-${stageIcons[i]}'),
                      color: Colors.white.withOpacity(0.5),
                      size: 18,
                    ),
                  ),
                ),
              ),
              title: Text(
                stage['name'] as String,
                style: GoogleFonts.dmSans(
                  color: Colors.white,
                  fontWeight:
                  isCurrentStage ? FontWeight.bold : FontWeight.normal,
                ),
              ),
              subtitle: Text(
                stage['description'] as String,
                style: GoogleFonts.dmSans(
                  color: isCurrentStage ? Colors.white70 : Colors.white60,
                  fontSize: 12,
                ),
              ),
              trailing: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: (stage['color'] as Color).withOpacity(0.15),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      '${stage['hours']}h',
                      style: GoogleFonts.dmSans(
                        color: stage['color'] as Color,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  if (isCurrentStage)
                    Container(
                      margin: EdgeInsets.only(top: 4),
                      padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        'ACTIVE',
                        style: GoogleFonts.dmSans(
                          color: Colors.green,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildStageMarker(
      String hour, Color color, bool isActive, bool isCurrent, IconData icon) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isActive ? color : Color(0xFF444444),
            border: isCurrent
                ? Border.all(color: Colors.white, width: 2)
                : isActive
                ? Border.all(color: color.withOpacity(0.3), width: 1)
                : null,
            boxShadow: isCurrent
                ? [
              BoxShadow(
                  color: color.withOpacity(0.5),
                  blurRadius: 8,
                  spreadRadius: 2)
            ]
                : null,
          ),
          child: Center(
            child: AnimatedSwitcher(
              duration: Duration(milliseconds: 300),
              child: isCurrent
                  ? Icon(
                icon,
                key: ValueKey('current'),
                color: Colors.white,
                size: 20,
              )
                  : isActive
                  ? Icon(
                Icons.check,
                key: ValueKey('active'),
                color: Colors.white,
                size: 20,
              )
                  : Text(
                hour,
                key: ValueKey('hour'),
                style: GoogleFonts.dmSans(
                  color: Colors.white70,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
        SizedBox(height: 4),
        Text(
          '${hour}h',
          style: GoogleFonts.dmSans(
            color: isCurrent ? color : Colors.white70,
            fontSize: 10,
          ),
        ),
      ],
    );
  }
}
