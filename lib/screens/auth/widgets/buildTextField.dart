import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

Widget buildTextField(
    TextEditingController controller,
    String hint,
    IconData icon, {
      bool obscureText = false,
      TextInputType? keyboardType,
      TextInputAction? textInputAction,
      Function(String)? onSubmitted,
      bool enabled = true,
      String? Function(String?)? validator,
    }) {
  return Container(
    decoration: BoxDecoration(
      color: Colors.white.withOpacity(0.08),
      borderRadius: BorderRadius.circular(16),
      border: Border.all(color: Colors.white.withOpacity(0.1)),
    ),
    child: TextFormField(
      controller: controller,
      obscureText: obscureText,
      keyboardType: keyboardType,
      textInputAction: textInputAction,
      onFieldSubmitted: onSubmitted,
      style: GoogleFonts.dmSans(color: Colors.white),
      enabled: enabled,
      validator: validator,
      decoration: InputDecoration(
        hintText: hint,
        hintStyle: GoogleFonts.dmSans(color: Colors.white54),
        prefixIcon: Icon(icon, color: Colors.white54),
        border: InputBorder.none,
        contentPadding: const EdgeInsets.symmetric(vertical: 16),
        errorStyle: GoogleFonts.dmSans(
          color: Colors.redAccent,
          fontSize: 12,
        ),
      ),
    ),
  );
}