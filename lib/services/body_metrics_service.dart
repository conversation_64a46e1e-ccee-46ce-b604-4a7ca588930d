import 'package:hive_flutter/hive_flutter.dart';
import 'dart:convert';
import '../models/body_metric.dart';
import 'package:intl/intl.dart';

class BodyMetricsService {
  static const String _boxName = 'body_metrics';
  late Box<String> _box; // Store plain JSON strings
  bool _isInitialized = false;

  BodyMetricsService();

  Future<void> init() async {
    if (_isInitialized) return;
    // Adapters are registered in main.dart, just open the box
    _box = await Hive.openBox<String>(_boxName);
    _isInitialized = true;
  }

  Stream<BoxEvent> watchMetrics() {
    if (!_isInitialized) {
      throw StateError(
        'BodyMetricsService must be initialized before use. Call init() first.',
      );
    }
    return _box.watch();
  }

  Future<void> addMeasurement(BodyMetric metric) async {
    try {
      final key = _getKeyFromDate(metric.date);
      final json = jsonEncode(metric.toJson());
      await _box.put(key, json);
    } catch (e) {
      print('Error adding measurement: $e');
      throw Exception('Failed to add measurement: ${e.toString()}');
    }
  }

  Future<void> deleteMeasurement(DateTime date) async {
    try {
      final key = _getKeyFromDate(date);
      await _box.delete(key);
    } catch (e) {
      print('Error deleting measurement: $e');
      throw Exception('Failed to delete measurement: ${e.toString()}');
    }
  }

  List<BodyMetric> getAllMeasurements() {
    try {
      final metrics = <BodyMetric>[];
      for (final json in _box.values) {
        try {
          final decoded = jsonDecode(json);
          metrics.add(BodyMetric.fromJson(decoded));
        } catch (e) {
          print('Error reading a body metric: $e');
        }
      }
      return metrics..sort((a, b) => a.date.compareTo(b.date));
    } catch (e) {
      print('Error getting all measurements: $e');
      return [];
    }
  }

  String _getKeyFromDate(DateTime date) {
    return DateFormat('yyyy-MM-dd-HH-mm-ss').format(date);
  }

  bool isBoxOpen() {
    return _isInitialized && _box.isOpen;
  }

  Future<void> reinitialize() async {
    _isInitialized = false;
    if (_box.isOpen) {
      await _box.close();
    }
    await init();
  }

  bool get isInitialized => _isInitialized;

  Future<void> close() async {
    if (_isInitialized && _box.isOpen) {
      await _box.close();
    }
    _isInitialized = false;
  }
}
