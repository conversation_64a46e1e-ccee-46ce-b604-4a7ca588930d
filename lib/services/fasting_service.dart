import 'package:hive_flutter/hive_flutter.dart';
import 'dart:convert';
import '../models/fasting_day.dart';
import 'dart:async';

class FastingService {
  static const String _boxName = 'fasting_days';
  late Box<String> _box; // Store plain JSON strings
  final _controller = StreamController<bool>.broadcast();

  FastingService();

  Future<void> init() async {
    // Adapters are registered in main.dart, just open the box
    _box = await Hive.openBox<String>(_boxName);
  }

  Future<void> saveFastingDay(FastingDay day) async {
    final key = day.id;
    final json = jsonEncode(day.toJson());
    await _box.put(key, json);
  }

  FastingDay? getFastingDay(DateTime date) {
    try {
      final key = _getKeyFromDate(date);
      final json = _box.get(key);

      if (json == null) {
        // Check for active fast from yesterday
        final yesterdayKey = _getKeyFromDate(
          date.subtract(const Duration(days: 1)),
        );
        final yesterdayJson = _box.get(yesterdayKey);

        if (yesterdayJson != null) {
          try {
            final yesterday = FastingDay.fromJson(jsonDecode(yesterdayJson));
            if (yesterday.isActive) {
              return yesterday;
            }
          } catch (e) {
            print('Error reading yesterday\'s data: $e');
          }
        }
        return null;
      }

      try {
        return FastingDay.fromJson(jsonDecode(json));
      } catch (e) {
        print('Error reading today\'s data: $e');
        _box.delete(key);
        return null;
      }
    } catch (e) {
      print('Error in getFastingDay: $e');
      return null;
    }
  }

  FastingDay? getFastingDayById(String id) {
    try {
      final json = _box.get(id);
      if (json == null) return null;
      return FastingDay.fromJson(jsonDecode(json));
    } catch (e) {
      print('Error in getFastingDayById: $e');
      return null;
    }
  }

  List<FastingDay> getAllFastingDays() {
    try {
      final result = <FastingDay>[];
      for (final json in _box.values) {
        try {
          result.add(FastingDay.fromJson(jsonDecode(json)));
        } catch (e) {
          print('Error reading a fasting day: $e');
        }
      }
      return result..sort((a, b) => b.date.compareTo(a.date));
    } catch (e) {
      print('Error in getAllFastingDays: $e');
      return [];
    }
  }

  List<FastingDay> getCompletedFastingDays() {
    return getAllFastingDays().where((day) => day.completed).toList()
      ..sort((a, b) => b.date.compareTo(a.date));
  }

  Stream<BoxEvent> watchFastingDays() {
    return _box.watch();
  }

  Future<void> updateFastingWindow({
    required DateTime date,
    required String startTime,
    required String endTime,
    required bool isActive,
    required bool completed,
    required String fastingProtocol,
    DateTime? startDate,
    DateTime? endDate,
    int? actualFastingMinutes,
  }) async {
    final key = _getKeyFromDate(date);
    final existingDay = getFastingDay(date);
    final updatedDay =
        (existingDay ??
                FastingDay(
                  id: key,
                  date: date,
                  startTime: startTime,
                  endTime: endTime,
                  isActive: isActive,
                  completed: completed,
                  fastingProtocol: fastingProtocol,
                ))
            .copyWith(
              startTime: startTime,
              endTime: endTime,
              isActive: isActive,
              completed: completed,
              startDate: startDate,
              endDate: endDate,
              actualFastingMinutes: actualFastingMinutes,
              fastingProtocol: fastingProtocol,
            );
    await saveFastingDay(updatedDay);
    _controller.add(true);
  }

  Future<void> updateDailyRoutine({
    required DateTime date,
    double? waterIntake,
    int? steps,
    double? exercise,
    List<String>? achievements,
  }) async {
    final key = _getKeyFromDate(date);
    final existingDay = getFastingDay(date);
    if (existingDay != null) {
      final updatedDay = existingDay.copyWith(
        waterIntake: waterIntake ?? existingDay.waterIntake,
        steps: steps ?? existingDay.steps,
        exercise: exercise ?? existingDay.exercise,
        achievements: achievements ?? existingDay.achievements,
      );
      await saveFastingDay(updatedDay);
    }
  }

  Future<void> deleteFastingDay(FastingDay fastingDay) async {
    await _box.delete(fastingDay.id);
    _controller.add(true);
  }

  Future<void> clearAllData() async {
    await _box.clear();
  }

  String _getKeyFromDate(DateTime date) {
    return "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}";
  }

  Future<void> close() async {
    await _controller.close();
    await _box.close();
  }

  Future<void> addAchievement(DateTime date, String achievement) async {
    final key = _getKeyFromDate(date);
    final existingDay = getFastingDay(date);
    if (existingDay != null) {
      final currentAchievements = List<String>.from(existingDay.achievements);
      if (!currentAchievements.contains(achievement)) {
        currentAchievements.add(achievement);
        final updatedDay = existingDay.copyWith(
          achievements: currentAchievements,
        );
        await saveFastingDay(updatedDay);
      }
    }
  }
}
