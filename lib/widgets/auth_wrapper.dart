import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../blocs/auth/auth_bloc.dart';
import '../blocs/auth/auth_state.dart';
import '../blocs/settings/settings_bloc.dart';
import '../blocs/settings/settings_event.dart';
import '../blocs/fasting/fasting_bloc.dart';
import '../blocs/fasting/fasting_event.dart';
import '../screens/home/<USER>';
import '../screens/splash/splash_screen.dart';
import '../route/router_constants.dart';

/// A wrapper widget that handles authentication state and proper initialization
class AuthWrapper extends StatefulWidget {
  const AuthWrapper({Key? key}) : super(key: key);

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  bool _hasInitialized = false;

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is Authenticated && !_hasInitialized) {
          _initializeUserData();
          _hasInitialized = true;
        } else if (state is Unauthenticated) {
          _hasInitialized = false;
        }
      },
      builder: (context, state) {
        if (state is AuthInitial || state is AuthLoading) {
          return const SplashScreen();
        } else if (state is Authenticated) {
          return const HomePage();
        } else {
          // Navigate to sign in screen
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              context.go('/sign-in');
            }
          });
          return const SplashScreen();
        }
      },
    );
  }

  void _initializeUserData() {
    // Initialize settings and fasting data with a small delay to prevent memory exhaustion
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        try {
          context.read<SettingsBloc>().add(LoadSettings());
        } catch (e) {
          debugPrint('Error initializing settings: $e');
        }
      }
    });

    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        try {
          context.read<FastingBloc>().add(LoadFastingDay(DateTime.now()));
        } catch (e) {
          debugPrint('Error initializing fasting data: $e');
        }
      }
    });
  }
}
