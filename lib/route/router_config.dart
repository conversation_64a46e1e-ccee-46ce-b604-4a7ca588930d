import 'package:fasttime/screens/auth/auth_screen.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../screens/home/<USER>';
import '../screens/placeholder_screens.dart';
import '../blocs/auth/auth_bloc.dart';
import '../blocs/auth/auth_state.dart';
import '../screens/splash/splash_screen.dart';
import 'router_constants.dart';


final GoRouter router = GoRouter(
  initialLocation: '/',
  routes: [
    // Public route
    GoRoute(
      name: RouteConstants.splash,
      path: '/',
      builder: (context, state) => BlocBuilder<AuthBloc, AuthState>(
        builder: (context, authState) {
          if (authState is Authenticated) {
            return HomePage(); // Auto-navigate to home if logged in
          } else {
            return const SplashScreen(); // Show splash while loading
          }
        },
      ),
    ),

    GoRoute(
      name: RouteConstants.signIn,
      path: '/sign-in',
      builder: (context, state) => AuthScreen(),
    ),
    // Private routes
    GoRoute(
      name: RouteConstants.home,
      path: '/home',
      builder: (context, state) => HomePage(),
    ),
    GoRoute(
      name: RouteConstants.settings,
      path: '/settings',
      builder: (context, state) => SettingsScreen(),
    ),
  ],
);
