import 'package:fasttime/screens/auth/auth_screen.dart';
import 'package:go_router/go_router.dart';
import '../screens/home/<USER>';
import '../screens/placeholder_screens.dart';
import '../widgets/auth_wrapper.dart';
import 'router_constants.dart';

final GoRouter router = GoRouter(
  initialLocation: '/',
  routes: [
    // Public route
    GoRoute(
      name: RouteConstants.splash,
      path: '/',
      builder: (context, state) => const AuthWrapper(),
    ),

    GoRoute(
      name: RouteConstants.signIn,
      path: '/sign-in',
      builder: (context, state) => AuthScreen(),
    ),
    // Private routes
    GoRoute(
      name: RouteConstants.home,
      path: '/home',
      builder: (context, state) => HomePage(),
    ),
    GoRoute(
      name: RouteConstants.settings,
      path: '/settings',
      builder: (context, state) => SettingsScreen(),
    ),
  ],
);
